#!/bin/bash

# 测试接口分配管理前端功能
# 测试管理员接口分配管理页面的API调用

echo "🧪 测试接口分配管理前端功能"
echo "=================================="

# 管理员令牌
ADMIN_TOKEN="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

BASE_URL="http://localhost:8081"

echo ""
echo "📊 1. 测试获取接口分配统计信息"
echo "-----------------------------------"
curl -s -X GET "${BASE_URL}/api/v1/admin/express/price-interface/stats" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" | jq .

echo ""
echo "📋 2. 测试获取所有接口分配列表"
echo "-----------------------------------"
curl -s -X GET "${BASE_URL}/api/v1/admin/express/price-interface/allocations?page=1&page_size=20" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" | jq .

echo ""
echo "🔍 3. 测试获取特定供应商+快递公司的接口类型"
echo "-----------------------------------"
echo "查询 kuaidi100-STO 的接口类型："
curl -s -X GET "${BASE_URL}/api/v1/admin/express/price-interface/kuaidi100/STO" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" | jq .

echo ""
echo "查询 yuntong-JD 的接口类型："
curl -s -X GET "${BASE_URL}/api/v1/admin/express/price-interface/yuntong/JD" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" | jq .

echo ""
echo "✏️ 4. 测试设置接口类型"
echo "-----------------------------------"
echo "将 kuaidi100-YD 设置为实时查价接口："
curl -s -X POST "${BASE_URL}/api/v1/admin/express/price-interface" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "provider_code": "kuaidi100",
    "company_code": "YD",
    "interface_type": "QUERY_REALTIME_PRICE"
  }' | jq .

echo ""
echo "验证设置结果："
curl -s -X GET "${BASE_URL}/api/v1/admin/express/price-interface/kuaidi100/YD" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" | jq .

echo ""
echo "📊 5. 再次查看统计信息（验证变更）"
echo "-----------------------------------"
curl -s -X GET "${BASE_URL}/api/v1/admin/express/price-interface/stats" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" | jq .

echo ""
echo "🔄 6. 恢复原始设置"
echo "-----------------------------------"
echo "将 kuaidi100-YD 恢复为标准查价接口："
curl -s -X POST "${BASE_URL}/api/v1/admin/express/price-interface" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "provider_code": "kuaidi100",
    "company_code": "YD",
    "interface_type": "QUERY_PRICE"
  }' | jq .

echo ""
echo "✅ 接口分配管理前端功能测试完成！"
echo ""
echo "🌐 前端页面访问地址："
echo "   管理员登录: http://localhost:3007"
echo "   接口配置管理: http://localhost:3007/#/interface-config/allocation"
echo ""
echo "📝 测试总结："
echo "   ✅ 统计信息API正常"
echo "   ✅ 分配列表API正常"
echo "   ✅ 单个查询API正常"
echo "   ✅ 设置接口类型API正常"
echo "   ✅ 前端页面已构建完成"

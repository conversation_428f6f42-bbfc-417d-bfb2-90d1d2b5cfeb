// @title           Go-Kuaidi API
// @version         1.0
// @description     统一快递API是一个集成了多家快递公司的统一接口服务，旨在为开发者提供简单、统一的快递服务接入方式。
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.url    http://www.gokuaidi.com/support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      1jy097co02227.vicp.fun
// @BasePath  /

// @securityDefinitions.apikey  Bearer
// @in                          header
// @name                        Authorization
// @description                 JWT授权令牌，使用Bearer模式。示例："Authorization: Bearer {token}"

// @securityDefinitions.apikey  ClientID
// @in                          header
// @name                        X-Client-ID
// @description                 客户端ID

// @securityDefinitions.apikey  Signature
// @in                          header
// @name                        X-Signature
// @description                 请求签名

package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	_ "net/http/pprof" // 🚀 添加pprof性能分析支持
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	_ "github.com/lib/pq" // PostgreSQL驱动
	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/api/middleware"
	"github.com/your-org/go-kuaidi/api/router"
	_ "github.com/your-org/go-kuaidi/docs" // 导入自动生成的文档
	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/api"
	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/express"
	internal_handler "github.com/your-org/go-kuaidi/internal/handler"
	"github.com/your-org/go-kuaidi/internal/logging"
	"github.com/your-org/go-kuaidi/internal/memory"
	internal_middleware "github.com/your-org/go-kuaidi/internal/middleware"
	"github.com/your-org/go-kuaidi/internal/pool"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/security"
	"github.com/your-org/go-kuaidi/internal/service"
	callbackService "github.com/your-org/go-kuaidi/internal/service/callback"
	"github.com/your-org/go-kuaidi/internal/user"
	"github.com/your-org/go-kuaidi/internal/util"
	"github.com/your-org/go-kuaidi/internal/workorder"
	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

// 🔥 供应商管理器适配器，解决类型不匹配问题
type providerManagerAdapter struct {
	manager *adapter.ProviderManager
}

func (p *providerManagerAdapter) Get(name string) (interface{}, bool) {
	adapter, exists := p.manager.Get(name)
	return adapter, exists
}

// 🔥 快递公司服务适配器，解决接口不匹配问题
type expressCompanyServiceAdapter struct {
	service express.ExpressCompanyService
}

// 实现 service.ExpressCompanyServiceInterface 接口
func (e *expressCompanyServiceAdapter) GetActiveCompanies(ctx context.Context) ([]*express.ExpressCompany, error) {
	return e.service.GetActiveCompanies(ctx)
}

func main() {
	// 🕐 设置应用程序时区为北京时间
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Fatalf("设置时区失败: %v", err)
	}
	time.Local = location
	log.Printf("✅ 应用程序时区已设置为: %s", location.String())

	// 初始化配置系统
	logger, _ := zap.NewProduction()
	configManager := config.NewConfigManager(logger)
	if err := configManager.Load(); err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 获取配置适配器以保持向后兼容
	configAdapter := config.NewConfigAdapter(configManager, logger)
	cfg, err := configAdapter.GetLegacyConfig()
	if err != nil {
		log.Fatalf("获取配置失败: %v", err)
	}

	// 🚀 初始化企业级日志管理器
	logConfig := logging.LoadLogConfigFromManager(configManager)
	logManager, err := logging.NewLogManager(logConfig)
	if err != nil {
		log.Fatalf("初始化日志管理器失败: %v", err)
	}
	defer logManager.Sync()

	// 获取各类日志器
	logger = logManager.GetAppLogger()
	errorLogger := logManager.GetErrorLogger()
	accessLogger := logManager.GetAccessLogger()
	// auditLogger := logManager.GetAuditLogger() // 暂时未使用

	logger.Info("✅ 企业级日志系统初始化成功",
		zap.String("level", logConfig.Level),
		zap.String("format", logConfig.Format),
		zap.String("output", logConfig.Output),
		zap.String("app_log", logConfig.FilePath),
		zap.String("error_log", logConfig.ErrorFilePath),
		zap.String("access_log", logConfig.AccessFilePath),
		zap.String("audit_log", logConfig.AuditFilePath))

	// 连接数据库
	db, err := connectDB(cfg.Database)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 创建GORM数据库连接
	gormDB, err := connectGormDB(cfg.Database)
	if err != nil {
		log.Fatalf("连接GORM数据库失败: %v", err)
	}

	// 🔄 创建回调数据库连接
	callbackGormDB, err := connectGormDB(cfg.CallbackDatabase)
	if err != nil {
		logger.Warn("连接回调数据库失败，将使用主数据库", zap.Error(err))
		callbackGormDB = gormDB // 回退到主数据库
	} else {
		logger.Info("✅ 回调数据库连接成功",
			zap.String("connection_string", cfg.CallbackDatabase.ConnectionString))

		// 🔍 测试查询回调数据库中的记录数
		var count int64
		if err := callbackGormDB.Table("callback_raw_data").Count(&count).Error; err != nil {
			logger.Error("❌ 测试查询回调数据库失败", zap.Error(err))
		} else {
			logger.Info("🔍 回调数据库记录数", zap.Int64("count", count))
		}
	}

	// 连接Redis
	rdb, err := connectRedis(cfg.Redis)
	if err != nil {
		log.Fatalf("连接Redis失败: %v", err)
	}
	defer rdb.Close()

	// 🚀 初始化数据库优化服务
	optimizationService := service.NewDatabaseOptimizationService(db, rdb, logger)
	ctx := context.Background()
	if err := optimizationService.Start(ctx); err != nil {
		logger.Error("启动数据库优化服务失败", zap.Error(err))
	} else {
		logger.Info("数据库优化服务已启动")
	}

	// 创建快递公司映射服务
	expressCompanyRepository := express.NewPostgresExpressCompanyRepository(db, logger)
	expressMappingService := express.NewExpressMappingService(expressCompanyRepository, logger)

	// 🔥 修复：创建快递公司映射缓存服务
	expressMappingCacheService := express.NewExpressMappingCacheService(expressCompanyRepository, logger)

	// 🔥 修复：创建快递公司服务，注入缓存服务以支持状态变更时的缓存刷新
	expressCompanyService := express.NewDefaultExpressCompanyService(expressCompanyRepository, expressMappingCacheService, logger)

	// 🚀 创建系统配置仓储和服务（企业级数据库配置管理）
	systemConfigRepository := repository.NewPostgresSystemConfigRepository(db)
	systemConfigService := service.NewDefaultSystemConfigService(systemConfigRepository, logger)

	// 🚀 创建配置适配器（解决循环依赖问题）
	var expressConfigAdapter express.ConfigServiceInterface = express.NewConfigAdapter(systemConfigService, logger)

	// 🚀 创建企业级状态管理器V2（避免循环依赖）
	expressCompanyStatusManager := express.NewDefaultExpressCompanyStatusManagerV2(
		expressCompanyRepository,
		expressConfigAdapter,
		expressMappingCacheService,
		logger,
	)

	// 验证状态管理器配置
	if err := expressCompanyStatusManager.ValidateConfiguration(context.Background()); err != nil {
		logger.Warn("状态管理器配置验证失败，但继续启动", zap.Error(err))
	} else {
		logger.Info("状态管理器配置验证通过")
	}

	// 🚀 将状态管理器集成到快递公司服务中
	if defaultExpressService, ok := expressCompanyService.(*express.DefaultExpressCompanyService); ok {
		defaultExpressService.SetStatusManager(expressCompanyStatusManager)
		logger.Info("状态管理器已集成到快递公司服务")

		// 🚀 新增：创建并设置接口分配器
		interfaceAllocator := express.NewPriceInterfaceAllocator(db, logger)
		defaultExpressService.SetInterfaceAllocator(interfaceAllocator)
		logger.Info("接口分配器已集成到快递公司服务")
	} else {
		logger.Warn("无法将状态管理器集成到快递公司服务：类型断言失败")
	}

	// 预热状态管理器缓存
	go func() {
		time.Sleep(5 * time.Second) // 等待系统完全启动
		if err := expressCompanyStatusManager.WarmupCache(context.Background()); err != nil {
			logger.Warn("状态管理器缓存预热失败", zap.Error(err))
		}
	}()

	// 🚀 创建供应商配置服务（企业级数据库配置管理）
	providerConfigService := service.NewProviderConfigService(systemConfigService, logger)

	// 创建动态供应商管理器（支持热更新）
	providerManager := createDynamicProviderManager(providerConfigService, systemConfigService, expressMappingService, expressCompanyRepository, logger)

	// 启动动态管理器
	startCtx := context.Background()
	if err := providerManager.StartDynamicManager(startCtx); err != nil {
		log.Fatalf("启动动态供应商管理器失败: %v", err)
	}

	// 注册优雅关闭处理
	defer func() {
		if err := providerManager.StopDynamicManager(ctx); err != nil {
			logger.Error("停止动态供应商管理器失败", zap.Error(err))
		}
	}()

	// 创建仓库
	orderRepository := repository.NewPostgresOrderRepository(db)
	balanceRepository := repository.NewPostgresBalanceRepository(db)

	// 初始化认证组件
	privateKey, publicKey, err := auth.EnsureRSAKeyPair(cfg.Auth.PrivateKeyPath, cfg.Auth.PublicKeyPath)
	if err != nil {
		log.Fatalf("初始化认证密钥失败: %v", err)
	}

	// 创建令牌黑名单
	tokenBlacklist := auth.NewRedisTokenBlacklist(rdb, "token_blacklist")

	// 创建认证服务
	tokenExpiry := time.Duration(cfg.Auth.TokenExpirySeconds) * time.Second
	if tokenExpiry < 10*time.Minute {
		// 配置缺失、解析失败或误配小于10分钟时，强制回退 1 小时
		logger.Warn("Token expiry too short, forcing 1 hour",
			zap.Duration("configured", tokenExpiry),
			zap.Int("seconds", cfg.Auth.TokenExpirySeconds))
		tokenExpiry = time.Hour
	}
	tokenService := auth.NewJWTService(privateKey, publicKey, cfg.Auth.Issuer, cfg.Auth.Audience, tokenExpiry, tokenBlacklist)
	clientRepository := auth.NewPostgresClientRepository(db)
	clientService := auth.NewClientService(clientRepository)
	authController := auth.NewAuthController(clientService, tokenService, tokenExpiry)

	// 创建角色和权限存储库
	roleRepository := user.NewPostgresRoleRepository(db)
	permissionRepository := user.NewPostgresPermissionRepository(db)

	// 创建角色和权限服务
	roleService := user.NewRoleService(roleRepository, permissionRepository)
	permissionService := user.NewPermissionService(permissionRepository)

	// 创建配置管理器
	userConfigManager := config.NewConfigManager(logger)
	if err := userConfigManager.Load(); err != nil {
		log.Fatalf("加载用户配置管理器失败: %v", err)
	}

	// 创建验证服务
	validationRuleRepository := user.NewPostgresValidationRuleRepository(db, logger)
	validationService := user.NewDefaultValidationService(validationRuleRepository, logger)

	// 加载验证规则
	if err := validationService.LoadRules(); err != nil {
		logger.Warn("Failed to load validation rules", zap.Error(err))
	}

	// 创建用户服务（传递验证服务）
	userRepository := user.NewPostgresUserRepository(db)
	clientServiceAdapter := user.NewClientServiceAdapter(clientService)
	userService := user.NewUserService(userRepository, clientServiceAdapter, roleRepository, permissionRepository, validationService)
	userController := user.NewUserController(userService, clientService)

	// 创建管理员用户服务和处理器
	adminUserRepository := user.NewPostgresAdminUserRepository(db, logger)
	userAuditRepository := user.NewPostgresUserAuditRepository(db, logger)
	userAuditService := user.NewDefaultUserAuditService(userAuditRepository, logger)
	adminUserService := user.NewDefaultAdminUserService(
		userService.(*user.DefaultUserService),
		adminUserRepository,
		userAuditService,
		userConfigManager,
		logger,
	)
	adminUserHandler := handler.NewAdminUserHandler(adminUserService, logger)

	// 创建管理员认证处理器
	adminAuthHandler := handler.NewAdminAuthHandler(
		userService,
		userService.(user.UserRoleService),
		tokenService,
		logger,
		cfg.Auth.TokenExpirySeconds,
	)

	// 创建权限中间件
	permissionMiddleware := internal_middleware.NewPermissionMiddleware(userService.(user.UserRoleService))

	// 创建管理员权限中间件
	adminMiddleware := middleware.NewAdminMiddleware(userService.(user.UserRoleService), userConfigManager, logger)

	// 创建服务
	// 🚀 初始化性能优化组件
	pool.InitDefaultWorkerPool()

	// 初始化全局性能优化器（自动启动）
	_ = memory.GetGlobalGCOptimizer()
	_ = util.GetGlobalJSONOptimizer()

	logger.Info("✅ 性能优化组件初始化完成",
		zap.String("worker_pool", "高级自动扩缩容"),
		zap.String("gc_optimizer", "智能GC调优"),
		zap.String("json_optimizer", "高性能JSON处理"))

	// 🎯 创建地区黑名单服务（需要在priceService之前创建）
	regionBlacklistService := service.NewRegionBlacklistService(logger)

	priceService := service.NewPriceService(providerManager, expressMappingService, logger, expressCompanyRepository, regionBlacklistService)

	// 🎯 创建统一余额服务 - 简化架构，隐藏复杂性
	balanceConfig := service.GetEnhancedConfig() // 使用增强模式配置
	unifiedBalanceService := service.NewUnifiedBalanceService(
		balanceRepository,
		orderRepository,
		db,
		rdb,
		balanceConfig,
		logger,
	)

	// 使用统一余额服务
	var balanceService service.BalanceService = unifiedBalanceService
	logger.Info("✅ 统一余额服务已集成到主程序",
		zap.String("mode", string(balanceConfig.PerformanceMode)),
		zap.Bool("async_enabled", balanceConfig.EnableAsync),
		zap.Bool("sharding_enabled", balanceConfig.EnableSharding),
		zap.Bool("distributed_lock_enabled", balanceConfig.EnableLocking),
		zap.Bool("monitoring_enabled", balanceConfig.EnableMonitoring))

	// 创建配置服务、事务管理器和审计服务（预扣费需要）
	configService := service.NewConfigService()
	transactionManager := service.NewTransactionManager(db)
	auditService := service.NewAuditService(logger, service.AuditConfig{Enabled: false})

	// 创建价格验证配置服务
	priceValidationConfigService := service.NewPriceValidationConfigService(systemConfigService, logger)

	// 🎯 地区黑名单服务已在前面创建

	// orderService 将在 orderPriceValidationService 创建后初始化

	// 配置管理器已在上面初始化，这里不需要重复创建

	// 配置管理器已在上面初始化，这里不需要重复创建

	// 创建回调服务和处理器
	callbackRepository := repository.NewPostgresCallbackRepository(db)
	billingRepository := repository.NewPostgresBillingRepository(db)
	billingService := service.NewBillingService(orderRepository, billingRepository, balanceService, logger)

	// 🔥 新增：创建智能订单查找服务
	smartOrderFinder := service.NewSmartOrderFinder(orderRepository, logger)

	// 🔥 创建订单状态历史服务（需要在回调服务之前创建）
	statusHistoryRepository := repository.NewOrderStatusHistoryRepository(db)
	statusHistoryService := service.NewOrderStatusHistoryService(
		db,
		orderRepository,
		statusHistoryRepository,
		logger,
	)

	// 🔥 创建订单状态更新器（修复订单取消不退款问题）
	statusUpdater := service.NewOrderStatusUpdater(
		gormDB,
		orderRepository,
		statusHistoryService,
		logger,
	)

	callbackSvc := callbackService.NewUnifiedCallbackService(callbackRepository, orderRepository, billingService, balanceService, expressMappingService, expressCompanyRepository, smartOrderFinder, systemConfigService, statusUpdater, logger)

	// 注册回调适配器 - 使用正确的配置
	kuaidi100CallbackAdapter := callbackService.NewKuaidi100CallbackAdapter("")
	yidaCallbackAdapter := callbackService.NewYidaCallbackAdapter()

	// 🚀 企业级修复：从数据库获取云通回调配置
	var yuntongCallbackAdapter callbackService.ProviderCallbackAdapter
	if yuntongConfig, err := providerConfigService.GetYuntongConfig(context.Background()); err == nil {
		yuntongCallbackAdapter = callbackService.NewYuntongCallbackAdapter(yuntongConfig.EBusinessID, yuntongConfig.ApiKey)
		logger.Info("✅ 云通回调适配器初始化成功（数据库配置）")
	} else {
		logger.Error("❌ 云通回调适配器初始化失败，使用空适配器", zap.Error(err))
		yuntongCallbackAdapter = callbackService.NewYuntongCallbackAdapter("", "")
	}

	// 🚀 新增：从数据库获取菜鸟回调配置
	var cainiaoCallbackAdapter callbackService.ProviderCallbackAdapter
	if cainiaoConfig, err := providerConfigService.GetCainiaoConfig(context.Background()); err == nil {
		cainiaoCallbackAdapter = callbackService.NewCainiaoCallbackAdapter(cainiaoConfig.AccessCode)
		logger.Info("✅ 菜鸟裹裹回调适配器初始化成功（数据库配置）")
	} else {
		logger.Error("❌ 菜鸟裹裹回调适配器初始化失败，使用空适配器", zap.Error(err))
		cainiaoCallbackAdapter = callbackService.NewCainiaoCallbackAdapter("")
	}

	// 🚀 新增：从数据库获取快递鸟回调配置
	var kuaidiniaoCallbackAdapter callbackService.ProviderCallbackAdapter
	if kuaidiniaoConfig, err := providerConfigService.GetKuaidiNiaoConfig(context.Background()); err == nil {
		kuaidiniaoCallbackAdapter = callbackService.NewKuaidiNiaoCallbackAdapter(kuaidiniaoConfig.ApiKey)
		logger.Info("✅ 快递鸟回调适配器初始化成功（数据库配置）")
	} else {
		logger.Error("❌ 快递鸟回调适配器初始化失败，使用空适配器", zap.Error(err))
		kuaidiniaoCallbackAdapter = callbackService.NewKuaidiNiaoCallbackAdapter("")
	}

	callbackSvc.RegisterProviderAdapter("kuaidi100", kuaidi100CallbackAdapter)
	callbackSvc.RegisterProviderAdapter("yida", yidaCallbackAdapter)
	callbackSvc.RegisterProviderAdapter("yuntong", yuntongCallbackAdapter)
	callbackSvc.RegisterProviderAdapter("cainiao", cainiaoCallbackAdapter)
	callbackSvc.RegisterProviderAdapter("kuaidiniao", kuaidiniaoCallbackAdapter)

	// 🔥 新增：初始化智能重试服务管理器
	retryServiceManager := callbackService.NewRetryServiceManager(
		callbackRepository,
		systemConfigService,
		logger,
	)

	// 初始化重试服务
	if err := retryServiceManager.Initialize(); err != nil {
		logger.Fatal("初始化重试服务失败", zap.Error(err))
	}

	// 启动重试服务
	if err := retryServiceManager.Start(); err != nil {
		logger.Fatal("启动重试服务失败", zap.Error(err))
	}

	// 注册优雅关闭处理
	defer func() {
		if err := retryServiceManager.Stop(); err != nil {
			logger.Error("停止重试服务失败", zap.Error(err))
		}
	}()

	logger.Info("✅ 智能重试服务已启动")

	callbackHandler := handler.NewCallbackHandler(callbackSvc, logger)

	// 创建快递公司管理处理器
	expressCompanyHandler := handler.NewExpressCompanyHandler(
		expressCompanyService,
		logger,
		providerManager.GetDynamicManager(),
	)

	// 创建快递公司映射处理器
	expressMappingHandler := handler.NewExpressMappingHandler(expressMappingService, logger)

	// 🚀 创建重量档位缓存服务和处理器
	weightCacheRepository := repository.NewWeightTierCacheRepository(db, logger)

	// 为缓存仓储设置供应商状态检查器（使用统一配置管理）
	statusChecker := adapter.NewDatabaseProviderStatusChecker(systemConfigService, logger)
	weightCacheRepository.SetProviderStatusChecker(statusChecker)

	// 创建快递公司服务适配器（解决接口不匹配问题）
	expressCompanyServiceAdapter := &expressCompanyServiceAdapter{
		service: expressCompanyService,
	}

	weightCacheService := service.NewWeightTierCacheService(
		weightCacheRepository,
		providerManager,
		systemConfigService,
		expressCompanyServiceAdapter,
		logger,
	)

	// 🚀 创建事件驱动的缓存失效机制（高性能+实时同步）
	cacheEventManager := service.NewCacheEventManager(logger)

	// 创建映射缓存事件处理器
	mappingCacheEventHandler := service.NewMappingCacheEventHandler(
		expressMappingCacheService,
		weightCacheRepository,
		logger,
	)

	// 注册事件处理器
	cacheEventManager.RegisterHandler(service.EventCompanyStatusChanged, mappingCacheEventHandler)
	cacheEventManager.RegisterHandler(service.EventMappingChanged, mappingCacheEventHandler)
	cacheEventManager.RegisterHandler(service.EventProviderChanged, mappingCacheEventHandler)

	// 创建事件发布器
	eventPublisher := service.NewExpressCacheEventPublisher(cacheEventManager, logger)

	// 设置事件发布器到快递公司服务
	if defaultService, ok := expressCompanyService.(*express.DefaultExpressCompanyService); ok {
		defaultService.SetEventPublisher(eventPublisher)
	}

	// 🔥 关键修复：设置映射服务依赖，用于检查数据库中的is_supported字段
	// 这样可以防止查询已禁用的快递公司，避免无效的API调用
	if setMappingService, ok := weightCacheService.(interface {
		SetMappingService(mappingService *express.ExpressMappingCacheService)
	}); ok {
		setMappingService.SetMappingService(expressMappingCacheService)
		logger.Info("✅ 重量档位缓存服务已设置映射服务依赖")
	} else {
		logger.Error("❌ 重量档位缓存服务类型断言失败，无法设置映射服务依赖")
	}

	weightCacheHandler := internal_handler.NewWeightTierCacheHandler(weightCacheService, logger)
	logger.Info("✅ 重量档位缓存服务初始化成功")

	// ✅ 事件驱动的缓存失效机制已替代旧的MappingCacheInvalidator

	// 🔥 设置重量缓存服务到数据库优化服务，用于预热功能
	optimizationService.SetWeightCacheService(weightCacheService)
	logger.Info("✅ 数据库优化服务已设置重量缓存服务依赖")

	// 创建订单价格验证服务
	orderPriceValidationService := service.NewOrderPriceValidationService(
		priceValidationConfigService,
		weightCacheService,
		priceService,
		&providerManagerAdapter{manager: providerManager}, // 🔥 传入供应商管理器适配器，用于价格验证时直接调用供应商API
		regionBlacklistService,                            // 🎯 新增：传入地区黑名单服务，统一处理所有供应商查价失败
		expressCompanyRepository,                          // 🚀 新增：传入快递公司仓储，用于检查dedicated接口
		logger,
	)

	// 🚀 新增：创建下单尝试记录仓库
	orderAttemptRepo := repository.NewOrderAttemptRepository(db, logger)

	// 🔥 创建平台订单号生成器
	platformOrderGenerator := service.NewPlatformOrderGenerator(
		db,
		logger,
		&service.PlatformOrderConfig{
			Prefix:           "GK",
			CacheSize:        100,
			RetryAttempts:    3,
			RetryDelay:       100 * time.Millisecond,
			EnableMetrics:    true,
			EnableLocalCache: true,
		},
	)

	// 🔥 创建失败订单服务（使用平台订单号生成器）
	failedOrderService := service.NewFailedOrderServiceWithPlatformGenerator(
		orderRepository,
		systemConfigService,
		platformOrderGenerator,
		logger,
	)

	// 使用增强订单服务支持预扣费
	orderService := service.NewEnhancedOrderService(
		providerManager,
		priceService,
		orderRepository,
		unifiedBalanceService, // 直接使用统一余额服务
		configService,
		transactionManager,
		auditService,
		orderPriceValidationService, // 传入价格验证服务
		orderAttemptRepo,            // 🚀 新增：传入下单尝试记录仓库
		statusHistoryService,        // 🔥 新增：传入状态历史服务
		failedOrderService,          // 🔥 新增：传入失败订单服务
		platformOrderGenerator,      // 🔥 新增：传入平台订单号生成器
		logger,
	)

	// 注释：增强价格服务将在预约时间服务初始化后创建

	// 创建供应商运费查询服务
	providerShippingFeeService := service.NewProviderShippingFeeService(
		providerManager,
		logger,
	)

	// 创建价格验证服务
	priceValidationService := service.NewPriceValidationService(
		providerShippingFeeService,
		logger,
	)

	// 🚀 创建工单模块（集成统一回调转发）
	workOrderConfig := workorder.LoadWorkOrderConfig(cfg)
	// 🔥 类型适配：将UnifiedCallbackService适配为WorkOrderCallbackService
	var workOrderCallbackSvc service.WorkOrderCallbackService = callbackSvc
	workOrderModule, err := workorder.NewWorkOrderModuleWithCallback(db, orderRepository, workOrderCallbackSvc, workOrderConfig, logger)
	if err != nil {
		logger.Fatal("创建工单模块失败", zap.Error(err))
	}

	// 🚀 创建增强价格服务（带缓存）
	enhancedPriceService := service.NewEnhancedPriceService(
		providerManager,
		expressMappingService,
		expressCompanyRepository,
		weightCacheService, // 🔥 修复：使用已配置的缓存服务实例
		systemConfigService,
		regionBlacklistService, // 🎯 新增：传入地区黑名单服务，统一处理所有供应商查价失败
		logger,
	)
	logger.Info("✅ 增强价格服务初始化成功（已集成重量档位缓存）")

	// 🚀 为增强价格服务设置接口分配器
	if interfaceAllocator := express.NewPriceInterfaceAllocator(db, logger); interfaceAllocator != nil {
		enhancedPriceService.SetInterfaceAllocator(interfaceAllocator)
		logger.Info("✅ 接口分配器已集成到增强价格服务")
	}

	logger.Info("✅ 工单模块初始化成功（已集成统一回调转发）")

	// 🔥 创建工单回调转发器（修复数据流向）
	workOrderCallbackForwarder := service.NewWorkOrderCallbackForwarder(
		workOrderModule.Repository,
		callbackRepository,
		orderRepository,  // 🔥 传入订单仓库
		smartOrderFinder, // 🔥 新增：传入智能订单查找服务
		logger,
	)
	logger.Info("✅ 工单回调转发器创建成功")

	// 🔥 工单回调转发器已创建，将通过参数传递给统一回调路由

	// 🔥 创建服务注入中间件
	serviceInjectionMiddleware := middleware.NewServiceInjectionMiddleware(
		workOrderCallbackForwarder,
	)

	// 🔥 将服务注入中间件传递给工单模块
	workOrderModule.SetServiceInjectionMiddleware(serviceInjectionMiddleware)

	// 创建状态映射服务
	statusMappingService := service.NewStatusMappingService(logger)

	// 🔥 创建管理员订单服务（集成状态同步退款功能和优化仓储）
	adminOrderService := service.NewAdminOrderService(
		orderRepository,
		userRepository,
		expressCompanyRepository,
		auditService,
		systemConfigService,
		providerShippingFeeService,
		priceValidationService,
		statusMappingService,
		providerManager,
		unifiedBalanceService, // 直接使用统一余额服务
		db,                    // 🔥 添加数据库连接
		logger,
	)

	// 创建管理员订单处理器（保持原有功能）
	adminOrderHandler := handler.NewAdminOrderHandler(adminOrderService, logger)

	// 创建增强服务
	rateLimiterService := service.NewRateLimiterService(db, systemConfigService, logger)
	_ = service.NewEnhancedAuditService(db, systemConfigService, logger)
	_ = service.NewPerformanceMonitorService(db, systemConfigService, logger)

	// 创建管理员余额服务和处理器
	adminBalanceRepository := repository.NewPostgresAdminBalanceRepository(db)
	adminBalanceService := service.NewDefaultAdminBalanceService(
		adminBalanceRepository,
		balanceRepository,
		userRepository,
		db,
		logger,
		cfg,
		systemConfigService,
		rateLimiterService,
	)
	adminBalanceHandler := handler.NewAdminBalanceHandler(adminBalanceService, logger)

	// 创建系统配置处理器
	adminSystemConfigHandler := internal_handler.NewAdminSystemConfigHandler(
		systemConfigService,
		logger,
	)

	// 创建性能处理器
	performanceHandler := handler.NewPerformanceHandler()

	// 创建管理员仪表盘处理器适配器
	adminBalanceServiceAdapter := handler.NewAdminBalanceServiceAdapter(adminBalanceService)
	adminOrderServiceAdapter := handler.NewAdminOrderServiceAdapter(adminOrderService)

	// 创建管理员仪表盘处理器
	adminDashboardHandler := handler.NewAdminDashboardHandler(
		logger,
		adminUserService,
		adminBalanceServiceAdapter,
		adminOrderServiceAdapter,
		performanceHandler,
		db,
		rdb,
		nil, // monitoringService暂时传nil
	)

	// 🔄 创建原始回调管理服务和处理器
	rawCallbackService := service.NewRawCallbackService(callbackGormDB, logger)
	unifiedCallbackService := service.NewUnifiedCallbackService(callbackSvc, logger) // 🔥 修复：传入真正的回调服务
	adminRawCallbackHandler := handler.NewAdminRawCallbackHandler(
		rawCallbackService,
		unifiedCallbackService,
		logger,
	)

	// 创建管理员供应商重载处理器
	var adminProviderReloadHandler *handler.AdminProviderReloadHandler
	if providerManager.IsDynamic() {
		// 获取动态管理器实例
		dynamicManager := providerManager.GetDynamicManager()
		if dynamicManager != nil {
			adminProviderReloadHandler = handler.NewAdminProviderReloadHandler(
				dynamicManager,
				logger,
			)
		}
	}

	// 🎯 创建地区黑名单处理器（服务已在前面创建）
	regionBlacklistHandler := handler.NewRegionBlacklistHandler(regionBlacklistService, logger)

	// 🎯 将黑名单服务集成到重量缓存服务中
	if weightCacheService != nil {
		weightCacheService.SetBlacklistService(regionBlacklistService)
		logger.Info("✅ 地区黑名单服务已集成到重量缓存服务")
	}

	// 创建地址解析服务
	addressLibraryService := service.NewAddressLibraryService(logger, "data")
	addressParseService := service.NewAddressParseService(logger, addressLibraryService)

	// 加载地址库
	if err := addressLibraryService.LoadAddressLibrary(); err != nil {
		logger.Warn("加载地址库失败", zap.Error(err))
	}

	// 创建处理器
	orderHandler := handler.NewOrderHandler(orderService, priceService, weightCacheService, expressCompanyService, logger)
	authHandler := handler.NewAuthHandler(authController, userService, clientService, tokenService, cfg.Auth.TokenExpirySeconds)
	balanceHandler := handler.NewBalanceHandler(balanceService, nil)

	// 🚀 创建优化版余额服务和处理器（用于管理接口）
	defaultBalanceService := service.NewBalanceService(balanceRepository, orderRepository, db, nil)
	optimizedBalanceService := service.NewOptimizedBalanceService(
		defaultBalanceService.(*service.DefaultBalanceService), // 使用默认服务
		logger,
	)
	optimizedBalanceHandler := handler.NewOptimizedBalanceHandler(
		balanceHandler,
		optimizedBalanceService,
		logger,
	)

	billingHandler := handler.NewBillingHandler(billingService, logger)
	addressHandler := handler.NewAddressHandler(logger, addressParseService, addressLibraryService)

	// 🔥 创建订单状态历史处理器
	statusHistoryHandler := internal_handler.NewOrderStatusHistoryHandler(
		statusHistoryService,
		logger,
	)

	// 🚀 创建数据库优化处理器
	dbOptimizationHandler := handler.NewDatabaseOptimizationHandler(optimizationService, logger)

	// 创建角色和权限处理器
	roleHandler := api.NewRoleHandler(roleService)
	permissionHandler := api.NewPermissionHandler(permissionService)
	userRoleHandler := api.NewUserRoleHandler(userService.(user.UserRoleService), roleService)

	// 创建安全配置
	securityConfig := security.DefaultSecurityConfig()

	// 🔒 强制启用签名验证 - 企业级安全要求
	securityConfig.Signature.Enabled = true
	securityConfig.Signature.DisableInDevelopment = false     // 所有环境都启用签名验证
	securityConfig.Signature.TimestampValiditySeconds = 1800  // 🚀 优化：30分钟，适应高并发和网络延迟
	securityConfig.Signature.NonceValiditySeconds = 1800      // 🚀 优化：30分钟
	securityConfig.Signature.MaxRequestBodySize = 1024 * 1024 // 1MB

	// 🚀 从配置文件读取nonce验证设置
	disableNonceValidation := configManager.GetBool("security.signature.disable_nonce_validation")
	securityConfig.Signature.DisableNonceValidation = disableNonceValidation
	logger.Info("读取nonce验证配置", zap.Bool("disable_nonce_validation", disableNonceValidation))

	// 🔒 严格控制跳过路径 - 只允许必要的健康检查和OAuth端点
	securityConfig.Signature.SkipPaths = []string{
		"/health",
		"/oauth/token", // OAuth token获取不需要签名（使用client_secret验证）
	}

	// 🔧 从配置文件读取限速设置，而不是硬编码
	rateLimitEnabled := configManager.GetBool("security.rate_limit.enabled")
	logger.Info("读取限速配置", zap.Bool("rate_limit_enabled", rateLimitEnabled))

	// 配置其他安全功能
	securityConfig.RateLimit.Enabled = rateLimitEnabled // 从配置文件读取限速设置
	securityConfig.Audit.Enabled = true                 // 启用审计日志
	securityConfig.Headers.Enabled = true               // 启用安全头

	// 创建安全服务
	signatureService := security.NewSignatureService(securityConfig.Signature)
	rateLimitService := security.NewRateLimitService(rdb, securityConfig.RateLimit)

	// 🚀 创建企业级nonce管理器
	nonceConfig := security.NonceConfig{
		ValidityDuration:  time.Duration(securityConfig.Signature.NonceValiditySeconds) * time.Second,
		MaxNonceLength:    64,
		MinNonceLength:    16,
		RedisKeyPrefix:    "nonce:v2:",
		RedisTimeout:      3 * time.Second,
		BatchSize:         100,
		CleanupInterval:   10 * time.Minute,
		EnableStrictMode:  true,
		MaxNoncePerClient: 1000,
		EnableMetrics:     true,
		MetricsInterval:   1 * time.Minute,
	}
	nonceManager := security.NewNonceManager(nonceConfig, rdb, logger)
	logger.Info("✅ 企业级nonce管理器初始化成功")

	// 禁用审计服务
	securityConfig.Audit.Enabled = false
	securityAuditService := security.NewAuditService(nil, securityConfig.Audit)

	// 注册用户注册成功的回调函数，用于注册客户端密钥
	userController.RegisterOnRegisterSuccess(func(user *user.User, clientID, clientSecret string) {
		// 注册客户端密钥
		middleware.RegisterClientSecret(clientID, clientSecret)
	})

	// 设置路由
	r := router.SetupRouter(
		orderHandler,
		orderService, // 🚀 订单服务
		authHandler,
		userController,
		tokenService,
		gormDB, // 🔥 使用GORM数据库连接
		rdb,
		clientService,
		signatureService,
		nonceManager, // 🚀 新增nonce管理器
		rateLimitService,
		securityAuditService,
		securityConfig,
		priceService,
		enhancedPriceService, // 🚀 增强价格服务（带缓存）
		providerManager,      // 🚀 新增：供应商管理器
		roleHandler,
		permissionHandler,
		userRoleHandler,
		permissionMiddleware,
		balanceHandler,
		optimizedBalanceHandler,    // 添加优化版余额处理器
		billingHandler,             // 添加计费处理器
		addressHandler,             // 添加地址解析处理器
		platformOrderGenerator,     // 🔥 添加平台订单号生成器
		callbackHandler,            // 添加回调处理器
		adminUserHandler,           // 添加管理员用户处理器
		adminAuthHandler,           // 添加管理员认证处理器
		adminMiddleware,            // 添加管理员权限中间件
		expressCompanyHandler,      // 添加快递公司管理处理器
		expressMappingHandler,      // 添加快递公司映射处理器
		adminOrderHandler,          // 添加管理员订单处理器
		adminBalanceHandler,        // 添加管理员余额处理器
		adminSystemConfigHandler,   // 添加管理员系统配置处理器
		adminDashboardHandler,      // 添加管理员仪表盘处理器
		adminProviderReloadHandler, // 🚀 添加管理员供应商重载处理器
		regionBlacklistHandler,     // 🎯 添加地区黑名单处理器
		dbOptimizationHandler,      // 🚀 添加数据库优化处理器
		workOrderModule.Handler,    // 🚀 添加工单处理器
		workOrderCallbackForwarder, // 🔥 添加工单回调转发器
		statusHistoryHandler,       // 🔥 添加订单状态历史处理器
		expressMappingService,      // 🔥 添加快递公司映射服务
		expressCompanyRepository,   // 🔥 添加快递公司仓储
		expressCompanyService,      // 🚀 添加快递公司服务
		weightCacheHandler,         // 🚀 添加重量档位缓存处理器
		orderRepository,            // 🔥 添加订单仓储
		systemConfigService,        // 🔥 添加系统配置服务
		logger,                     // 🚀 添加日志记录器
		errorLogger,                // 🚀 添加错误日志记录器
		accessLogger,               // 🚀 添加访问日志记录器
	)

	// 设置文档路由（不受安全中间件影响）
	router.SetupDocRouter(r)

	// 🔄 设置原始回调管理路由
	if adminRawCallbackHandler != nil && adminMiddleware != nil {
		// 创建管理员原始回调管理路由组
		adminRawCallbackGroup := r.Group("/api/v1/admin/raw-callbacks")

		// 应用认证和管理员权限中间件
		adminRawCallbackGroup.Use(auth.JWTAuthMiddleware(tokenService))
		adminRawCallbackGroup.Use(adminMiddleware.RequireAdmin())

		{
			// 获取原始回调列表
			adminRawCallbackGroup.GET("/records", adminRawCallbackHandler.GetRawCallbackList)

			// 获取原始回调详情
			adminRawCallbackGroup.GET("/records/:id", adminRawCallbackHandler.GetRawCallbackDetail)

			// 重推单个原始回调
			adminRawCallbackGroup.POST("/retry/:id", adminRawCallbackHandler.RetryRawCallback)

			// 批量重推原始回调
			adminRawCallbackGroup.POST("/batch-retry", adminRawCallbackHandler.BatchRetryRawCallbacks)

			// 按条件批量重推
			adminRawCallbackGroup.POST("/batch-retry-by-condition", adminRawCallbackHandler.BatchRetryByCondition)

			// 获取原始回调统计
			adminRawCallbackGroup.GET("/statistics", adminRawCallbackHandler.GetRawCallbackStatistics)

			// 导出原始回调数据
			adminRawCallbackGroup.GET("/export", adminRawCallbackHandler.ExportRawCallbacks)
		}

		logger.Info("✅ 原始回调管理路由注册成功")
	}

	// 🔥 新增：设置回调重试路由
	// 由于我们需要创建路由文件，暂时先在这里手动添加基本路由
	retryGroup := r.Group("/api/v1/callback/retry")
	retryGroup.Use(auth.JWTAuthMiddleware(tokenService))
	{
		// 基本的重试状态查询路由
		retryGroup.GET("/status", func(c *gin.Context) {
			status := retryServiceManager.GetStatus()
			c.JSON(200, gin.H{
				"success": true,
				"message": "获取重试状态成功",
				"data":    status,
			})
		})
	}

	// 健康检查路由
	r.GET("/health/retry", func(c *gin.Context) {
		status := retryServiceManager.GetStatus()
		httpStatus := 200
		if !status.Running {
			httpStatus = 503
		}
		c.JSON(httpStatus, gin.H{
			"service":   "callback-retry",
			"status":    status,
			"timestamp": time.Now().Unix(),
			"healthy":   status.Running,
		})
	})

	logger.Info("✅ 回调重试路由已配置")

	// 创建HTTP服务器，使用性能优化配置
	srv := &http.Server{
		Addr:           fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:        r,
		ReadTimeout:    time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout:   time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:    time.Duration(cfg.Server.IdleTimeout) * time.Second,
		MaxHeaderBytes: cfg.Server.MaxHeaderBytes,
	}

	// 🔥 修复：改进HTTP服务器启动和错误处理
	serverErrors := make(chan error, 1)
	go func() {
		log.Printf("🚀 HTTP服务器正在启动，监听端口: %d", cfg.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("❌ HTTP服务器启动失败: %v", err)
			serverErrors <- err
		} else {
			log.Printf("✅ HTTP服务器正常关闭")
		}
	}()

	// 🔥 修复：等待中断信号或服务器错误
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	select {
	case err := <-serverErrors:
		log.Printf("❌ 服务器错误，准备关闭: %v", err)
	case sig := <-quit:
		log.Printf("🛑 接收到信号 %v，准备关闭服务器...", sig)
	}

	// 🔥 修复：优雅关闭服务器
	log.Println("🔄 开始优雅关闭服务器...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 🚀 停止缓存事件管理器
	log.Println("🔄 停止缓存事件管理器...")
	cacheEventManager.Stop()

	// 关闭HTTP服务器
	if err := srv.Shutdown(ctx); err != nil {
		log.Printf("❌ 服务器关闭失败: %v", err)
		// 🔥 修复：不要使用Fatal，而是记录错误并继续
	} else {
		log.Println("✅ HTTP服务器已优雅关闭")
	}

	log.Println("🏁 服务器已完全关闭")
}

// 连接数据库
func connectDB(cfg config.DatabaseConfig) (*sql.DB, error) {
	// 连接数据库
	db, err := sql.Open("postgres", cfg.ConnectionString)
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 设置连接池
	db.SetMaxOpenConns(cfg.MaxOpenConns)
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	db.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("测试数据库连接失败: %w", err)
	}

	return db, nil
}

// 连接GORM数据库
func connectGormDB(cfg config.DatabaseConfig) (*gorm.DB, error) {
	// 导入GORM PostgreSQL驱动
	gormDB, err := gorm.Open(postgres.Open(cfg.ConnectionString), &gorm.Config{
		Logger: gormLogger.Default.LogMode(gormLogger.Silent),
	})
	if err != nil {
		return nil, fmt.Errorf("连接GORM数据库失败: %w", err)
	}

	// 获取底层sql.DB并设置连接池参数
	sqlDB, err := gormDB.DB()
	if err != nil {
		return nil, fmt.Errorf("获取底层数据库连接失败: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)

	return gormDB, nil
}

// 连接Redis
func connectRedis(cfg config.RedisConfig) (*redis.Client, error) {
	// 解析连接字符串
	opts, err := redis.ParseURL(cfg.ConnectionString)
	if err != nil {
		return nil, fmt.Errorf("解析Redis连接字符串失败: %w", err)
	}

	// 设置数据库索引
	opts.DB = cfg.DB

	// 创建Redis客户端
	rdb := redis.NewClient(opts)

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("测试Redis连接失败: %w", err)
	}

	return rdb, nil
}

// createDynamicProviderManager 创建动态供应商管理器（支持热更新）
func createDynamicProviderManager(providerConfigService service.ProviderConfigService, systemConfigService service.SystemConfigService, mappingService express.ExpressMappingService, expressCompanyRepo express.ExpressCompanyRepository, logger *zap.Logger) *adapter.ProviderManager {
	// 创建适配器工厂（使用类型断言）
	configService := providerConfigService.(interface {
		GetKuaidi100Config(ctx context.Context) (*adapter.Kuaidi100Config, error)
		GetYidaConfig(ctx context.Context) (*adapter.YidaConfig, error)
		GetYuntongConfig(ctx context.Context) (*adapter.YuntongConfig, error)
		GetCainiaoConfig(ctx context.Context) (*adapter.CainiaoConfig, error)
		GetKuaidiNiaoConfig(ctx context.Context) (*adapter.KuaidiNiaoConfig, error)
		IsProviderEnabled(ctx context.Context, providerName string) (bool, error)
	})

	adapterFactory := adapter.NewDefaultProviderAdapterFactory(
		configService,
		mappingService,
		expressCompanyRepo,
		logger,
	)

	// 创建动态供应商管理器
	dynamicManager := adapter.NewDynamicProviderManager(
		adapterFactory,
		systemConfigService,
		logger,
	)

	// 创建包装器
	providerManager := adapter.NewDynamicProviderManagerWrapper(dynamicManager)

	logger.Info("✅ 动态供应商管理器创建成功")
	return providerManager
}

// 注意：旧版createProviderManager函数已删除
// 现在统一使用createProviderManagerFromDatabase从数据库获取配置
// 或使用createDynamicProviderManager创建支持热更新的动态管理器

// 注意：旧版initLogger函数已删除
// 现在使用企业级LogManager进行日志管理
// 支持分层日志输出、文件轮转、配置驱动等功能

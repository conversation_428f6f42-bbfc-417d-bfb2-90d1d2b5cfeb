package router

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/api/middleware"
	"github.com/your-org/go-kuaidi/internal/auth"
)

// ExpressCompanyRouterConfig 快递公司路由配置
type ExpressCompanyRouterConfig struct {
	ExpressCompanyHandler           *handler.ExpressCompanyHandler
	ExpressMappingHandler           *handler.ExpressMappingHandler
	PriceInterfaceAllocationHandler *handler.PriceInterfaceAllocationHandler // 🚀 新增：查价接口分配处理器
	AuthMiddleware                  *middleware.AuthMiddleware
	AdminMiddleware                 *middleware.AdminMiddleware
	TokenService                    auth.TokenService
}

// SetupExpressCompanyRouter 设置快递公司管理路由
func SetupExpressCompanyRouter(r *gin.Engine, config ExpressCompanyRouterConfig) {
	// 管理员快递公司管理路由组
	adminExpressGroup := r.Group("/api/v1/admin/express")
	adminExpressGroup.Use(config.AuthMiddleware.RequireAuth())
	adminExpressGroup.Use(config.AdminMiddleware.RequireAdmin())
	{
		// 快递公司管理
		companies := adminExpressGroup.Group("/companies")
		companies.Use(config.AdminMiddleware.RequireResourcePermission("express_company", "manage"))
		{
			companies.POST("", config.ExpressCompanyHandler.CreateCompany)       // 创建快递公司
			companies.GET("", config.ExpressCompanyHandler.GetCompanies)         // 获取快递公司列表
			companies.GET("/:id", config.ExpressCompanyHandler.GetCompany)       // 获取快递公司详情
			companies.PUT("/:id", config.ExpressCompanyHandler.UpdateCompany)    // 更新快递公司
			companies.DELETE("/:id", config.ExpressCompanyHandler.DeleteCompany) // 删除快递公司

			// 🚀 新增：状态管理端点
			companies.PATCH("/:company_code/status", config.ExpressCompanyHandler.UpdateCompanyStatus) // 更新快递公司状态
		}

		// 供应商管理
		providers := adminExpressGroup.Group("/providers")
		providers.Use(config.AdminMiddleware.RequireResourcePermission("express_provider", "manage"))
		{
			providers.POST("", config.ExpressCompanyHandler.CreateProvider)       // 创建供应商
			providers.GET("", config.ExpressCompanyHandler.GetProviders)          // 获取供应商列表
			providers.GET("/:id", config.ExpressCompanyHandler.GetProvider)       // 获取供应商详情
			providers.PUT("/:id", config.ExpressCompanyHandler.UpdateProvider)    // 更新供应商
			providers.DELETE("/:id", config.ExpressCompanyHandler.DeleteProvider) // 删除供应商

			// 🚀 新增：状态管理端点
			providers.PATCH("/:provider_code/status", config.ExpressCompanyHandler.UpdateProviderStatus) // 更新供应商状态
		}

		// 映射关系管理
		mappings := adminExpressGroup.Group("/mappings")
		mappings.Use(config.AdminMiddleware.RequireResourcePermission("express_mapping", "manage"))
		{
			mappings.POST("", config.ExpressCompanyHandler.CreateMapping)                              // 创建映射关系
			mappings.GET("", config.ExpressCompanyHandler.GetMappings)                                 // 获取映射关系列表
			mappings.GET("/:id", config.ExpressCompanyHandler.GetMapping)                              // 获取映射关系详情
			mappings.PUT("/:id", config.ExpressCompanyHandler.UpdateMapping)                           // 更新映射关系
			mappings.DELETE("/:id", config.ExpressCompanyHandler.DeleteMapping)                        // 删除映射关系
			mappings.GET("/company/:company_id", config.ExpressCompanyHandler.GetMappingsByCompany)    // 根据快递公司获取映射关系
			mappings.GET("/provider/:provider_id", config.ExpressCompanyHandler.GetMappingsByProvider) // 根据供应商获取映射关系

			// 🚀 新增：映射关系状态管理端点
			mappings.PATCH("/:company_code/:provider_code/status", config.ExpressCompanyHandler.UpdateMappingStatus) // 更新映射关系状态
		}

		// 服务管理
		services := adminExpressGroup.Group("/services")
		services.Use(config.AdminMiddleware.RequireResourcePermission("express_service", "manage"))
		{
			services.POST("", config.ExpressCompanyHandler.CreateService)                           // 创建服务
			services.GET("/company/:company_id", config.ExpressCompanyHandler.GetServicesByCompany) // 根据快递公司获取服务列表
			services.PUT("/:id", config.ExpressCompanyHandler.UpdateService)                        // 更新服务
			services.DELETE("/:id", config.ExpressCompanyHandler.DeleteService)                     // 删除服务
		}

		// 配置管理
		configs := adminExpressGroup.Group("/configs")
		configs.Use(config.AdminMiddleware.RequireResourcePermission("express_config", "manage"))
		{
			configs.POST("", config.ExpressCompanyHandler.CreateConfig)                             // 创建配置
			configs.GET("/company/:company_id", config.ExpressCompanyHandler.GetConfigsByCompany)   // 根据快递公司获取配置列表
			configs.GET("/company/:company_id/:config_key", config.ExpressCompanyHandler.GetConfig) // 获取特定配置
			configs.PUT("/:id", config.ExpressCompanyHandler.UpdateConfig)                          // 更新配置
			configs.DELETE("/:id", config.ExpressCompanyHandler.DeleteConfig)                       // 删除配置
		}

		// 审计日志查询
		auditLogs := adminExpressGroup.Group("/audit-logs")
		auditLogs.Use(config.AdminMiddleware.RequireResourcePermission("express_audit", "read"))
		{
			auditLogs.GET("", config.ExpressCompanyHandler.GetAuditLogs) // 获取审计日志列表
		}

		// 🚀 新增：查价接口分配管理
		if config.PriceInterfaceAllocationHandler != nil {
			priceInterface := adminExpressGroup.Group("/price-interface")
			priceInterface.Use(config.AdminMiddleware.RequireResourcePermission("express_config", "manage"))
			{
				priceInterface.POST("", config.PriceInterfaceAllocationHandler.SetPriceInterface)              // 设置快递公司查价接口类型
				priceInterface.GET("/:company_code", config.PriceInterfaceAllocationHandler.GetPriceInterface) // 获取快递公司查价接口类型
				priceInterface.GET("/allocations", config.PriceInterfaceAllocationHandler.GetAllAllocations)   // 获取所有接口分配信息
				priceInterface.GET("/stats", config.PriceInterfaceAllocationHandler.GetAllocationStats)        // 获取接口分配统计信息
			}
		}
	}

	// 普通用户快递公司查询路由组（只读）
	userExpressGroup := r.Group("/api/v1/express")
	userExpressGroup.Use(config.AuthMiddleware.RequireAuth())
	{
		// 快递公司查询（普通用户只能查看启用的快递公司）
		companies := userExpressGroup.Group("/companies")
		companies.Use(auth.ScopeRequiredMiddleware("express:read"))
		{
			companies.GET("", config.ExpressCompanyHandler.GetActiveCompanies)     // 获取启用的快递公司列表
			companies.GET("/:code", config.ExpressCompanyHandler.GetCompanyByCode) // 根据代码获取快递公司
		}

		// 供应商查询（普通用户只能查看启用的供应商）
		providers := userExpressGroup.Group("/providers")
		providers.Use(auth.ScopeRequiredMiddleware("express:read"))
		{
			providers.GET("", config.ExpressCompanyHandler.GetActiveProviders)      // 获取启用的供应商列表
			providers.GET("/:code", config.ExpressCompanyHandler.GetProviderByCode) // 根据代码获取供应商
		}

		// 映射关系查询（普通用户查询供应商特定代码）
		mappings := userExpressGroup.Group("/mappings")
		mappings.Use(auth.ScopeRequiredMiddleware("express:read"))
		{
			// 获取供应商特定的快递公司代码
			mappings.GET("/provider-code/:company_code/:provider_code", config.ExpressCompanyHandler.GetProviderCompanyCode)
		}

		// 新的映射服务路由（使用专门的映射处理器）
		if config.ExpressMappingHandler != nil {
			mapping := userExpressGroup.Group("/mapping")
			mapping.Use(auth.ScopeRequiredMiddleware("express:read"))
			{
				// 获取供应商支持的快递公司列表
				mapping.GET("/providers/:provider_code/companies", config.ExpressMappingHandler.GetSupportedCompanies)

				// 获取快递公司映射信息
				mapping.GET("/companies/:company_code/providers/:provider_code", config.ExpressMappingHandler.GetCompanyMapping)

				// 获取供应商特定的快递公司代码
				mapping.GET("/companies/:company_code/providers/:provider_code/code", config.ExpressMappingHandler.GetProviderCompanyCode)

				// 获取快递公司的首选供应商
				mapping.GET("/companies/:company_code/preferred-provider", config.ExpressMappingHandler.GetPreferredProvider)

				// 获取体积重量系数
				mapping.GET("/companies/:company_code/volume-weight-ratio", config.ExpressMappingHandler.GetVolumeWeightRatio)
			}
		}
	}

	// 公开的快递公司信息接口（不需要认证，用于前端展示）
	publicExpressGroup := r.Group("/api/v1/public/express")
	{
		// 获取启用的快递公司列表（基础信息）
		publicExpressGroup.GET("/companies", config.ExpressCompanyHandler.GetPublicCompanies)

		// 获取快递公司基础信息
		publicExpressGroup.GET("/companies/:code", config.ExpressCompanyHandler.GetPublicCompanyByCode)
	}

	// 管理员映射缓存管理路由
	if config.ExpressMappingHandler != nil {
		adminMappingGroup := r.Group("/api/v1/admin/express/mapping")
		adminMappingGroup.Use(config.AuthMiddleware.RequireAuth())
		adminMappingGroup.Use(config.AdminMiddleware.RequireAdmin())
		{
			// 刷新映射缓存
			adminMappingGroup.POST("/cache/refresh", config.ExpressMappingHandler.RefreshCache)
		}
	}
}

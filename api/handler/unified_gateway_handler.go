package handler

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid" // 🎫 新增：UUID支持
	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/user"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// UnifiedGatewayHandler 统一网关处理器
// 🚀 参考易达API设计，提供统一的API入口
type UnifiedGatewayHandler struct {
	priceService service.PriceServiceInterface
	orderHandler *OrderHandler
	orderService *service.OrderService
	// enhancedOrderQueryService 已删除 - 使用 SmartOrderFinder 替代
	workOrderService         service.WorkOrderService    // 🎫 新增：工单服务
	failedOrderService       *service.FailedOrderService // 🔥 新增：失败订单服务
	realtimePriceHandler     *RealtimePriceHandler       // 🚀 新增：实时查价处理器
	tokenService             auth.TokenService
	expressMappingService    express.ExpressMappingService
	expressCompanyRepository express.ExpressCompanyRepository
	expressCompanyService    express.ExpressCompanyService        // 🚀 新增：快递公司服务
	systemConfigService      adapter.SystemConfigServiceInterface // 🚀 新增：系统配置服务
	userRepo                 user.UserRepository                  // ⭐ 新增：用户仓储，用于client_id→user_id映射
	logger                   *zap.Logger
}

// UnifiedRequest 统一请求结构
type UnifiedRequest struct {
	// 🔐 认证参数
	Username    string `json:"username"`    // 用户名（Web用户）或客户端ID（API客户端）
	Timestamp   string `json:"timestamp"`   // 时间戳
	Sign        string `json:"sign"`        // 签名（API客户端必需）
	AccessToken string `json:"accessToken"` // JWT Token（Web用户必需）- 修复：使用驼峰命名匹配前端

	// 🚀 业务参数
	APIMethod      string      `json:"apiMethod"`      // API方法名
	BusinessParams interface{} `json:"businessParams"` // 业务参数

	// 🌐 客户端类型标识
	ClientType string `json:"clientType"` // "web" | "api" | "admin"
}

// UnifiedResponse 统一响应结构
type UnifiedResponse struct {
	Code    int         `json:"code"`    // 响应码
	Message string      `json:"msg"`     // 响应消息
	Data    interface{} `json:"data"`    // 响应数据
	Success bool        `json:"success"` // 是否成功
}

// CreateOrderBusinessParams 订单创建业务参数
type CreateOrderBusinessParams struct {
	// 订单信息
	UserID          string `json:"user_id"`           // 用户ID (必填)
	CustomerOrderNo string `json:"customer_order_no"` // 🔥 新增：客户自定义订单号 (可选)
	OrderCode       string `json:"order_code"`        // 统一下单代码 (从查价接口获取)

	// 寄件人信息 (必填)
	SenderName     string `json:"sender_name"`     // 寄件人姓名
	SenderMobile   string `json:"sender_mobile"`   // 寄件人手机
	SenderProvince string `json:"sender_province"` // 寄件省
	SenderCity     string `json:"sender_city"`     // 寄件市
	SenderDistrict string `json:"sender_district"` // 寄件区/县
	SenderAddress  string `json:"sender_address"`  // 寄件详细地址

	// 收件人信息 (必填)
	ReceiverName     string `json:"receiver_name"`     // 收件人姓名
	ReceiverMobile   string `json:"receiver_mobile"`   // 收件人手机
	ReceiverProvince string `json:"receiver_province"` // 收件省
	ReceiverCity     string `json:"receiver_city"`     // 收件市
	ReceiverDistrict string `json:"receiver_district"` // 收件区/县
	ReceiverAddress  string `json:"receiver_address"`  // 收件详细地址

	// 包裹信息
	Weight    float64 `json:"weight"`     // 重量(kg) (必填)
	Volume    float64 `json:"volume"`     // 体积(m³) (可选)
	Length    float64 `json:"length"`     // 长度(cm) (可选)
	Width     float64 `json:"width"`      // 宽度(cm) (可选)
	Height    float64 `json:"height"`     // 高度(cm) (可选)
	Quantity  int     `json:"quantity"`   // 包裹数量 (可选，默认1)
	GoodsName string  `json:"goods_name"` // 物品名称 (可选，默认"物品")

	// 其他信息
	PayMethod     int     `json:"pay_method"`     // 支付方式：0-寄付，1-到付，2-月结 (可选，默认0)
	Remark        string  `json:"remark"`         // 备注 (可选)
	InsureValue   int     `json:"insure_value"`   // 保价金额(分) (可选)
	ExpectedPrice float64 `json:"expected_price"` // 期望价格 (可选，用于一致性验证)

	// 预约时间相关字段
	PickupStartTime string `json:"pickup_start_time,omitempty"` // 预约开始时间（ISO 8601格式，如：2025-06-30T09:00:00Z）(可选)
	PickupEndTime   string `json:"pickup_end_time,omitempty"`   // 预约结束时间（ISO 8601格式，如：2025-06-30T11:00:00Z）(可选)

	// 🔥 新增：菜鸟裹裹专用字段
	OutOrderId string `json:"out_order_id,omitempty"` // 外部订单ID（菜鸟裹裹专用，可选）
}

// CancelOrderBusinessParams 取消订单业务参数
type CancelOrderBusinessParams struct {
	OrderNo         string `json:"order_no"`          // 🔥 智能订单号：支持平台订单号、客户订单号、供应商订单号 (可选)
	TrackingNo      string `json:"tracking_no"`       // 运单号 (可选)
	PlatformOrderNo string `json:"platform_order_no"` // 🔥 新增：明确指定平台订单号 (可选)
	Reason          string `json:"reason"`            // 取消原因 (必填)
}

// QueryOrderBusinessParams 查询订单业务参数
type QueryOrderBusinessParams struct {
	OrderNo         string `json:"order_no"`          // 🔥 智能订单号：支持平台订单号、客户订单号、供应商订单号 (可选)
	TrackingNo      string `json:"tracking_no"`       // 运单号 (可选)
	PlatformOrderNo string `json:"platform_order_no"` // 🔥 新增：明确指定平台订单号 (可选)
	CustomerOrderNo string `json:"customer_order_no"` // 🔥 新增：明确指定客户订单号 (可选)
	ProviderOrderNo string `json:"provider_order_no"` // 🔥 新增：明确指定供应商订单号 (可选)
}

// TrackQueryBusinessParams 物流轨迹查询业务参数
type TrackQueryBusinessParams struct {
	OrderNo    string `json:"order_no"`    // 平台订单号（可选）
	TrackingNo string `json:"tracking_no"` // 运单号（必填）
	Phone      string `json:"phone"`       // 收、寄件人的电话号码（可选）
	From       string `json:"from"`        // 出发地城市（可选）
	To         string `json:"to"`          // 目的地城市（可选）
	PollToken  string `json:"poll_token"`  // 查询密钥（可选，用于免费查询）
}

// CreateOrderResponseData 订单创建响应数据
type CreateOrderResponseData struct {
	Success         bool   `json:"success"`           // 是否成功
	Code            int    `json:"code"`              // 状态码
	Message         string `json:"message"`           // 消息
	PlatformOrderNo string `json:"platform_order_no"` // 🔥 平台生成的全局唯一订单号
	CustomerOrderNo string `json:"customer_order_no"` // 🔥 客户订单号
	OrderNo         string `json:"order_no"`          // 🔥 主要展示字段：平台订单号（展示给用户）
	WaybillNo       string `json:"waybill_no"`        // 运单号
	ExpressCode     string `json:"express_code"`      // 快递公司代码
	ExpressName     string `json:"express_name"`      // 快递公司名称
	Price           string `json:"price"`             // 价格
	// 注意：移除了provider_order_no字段，供应商内部订单号不应展示给用户
}

// CancelOrderResponseData 取消订单响应数据
type CancelOrderResponseData struct {
	Success   bool   `json:"success"`    // 是否成功
	Code      int    `json:"code"`       // 状态码
	Message   string `json:"message"`    // 消息
	OrderNo   string `json:"order_no"`   // 订单号
	Status    string `json:"status"`     // 订单状态
	RequestID string `json:"request_id"` // 请求ID
}

// QueryOrderResponseData 查询订单响应数据
type QueryOrderResponseData struct {
	Success bool                 `json:"success"`        // 是否成功
	Code    int                  `json:"code"`           // 状态码
	Message string               `json:"message"`        // 消息
	Data    *EnhancedOrderDetail `json:"data,omitempty"` // 🔥 增强的订单详情数据
	Meta    *QueryMetadata       `json:"meta,omitempty"` // 🔥 查询元数据
}

// EnhancedOrderDetail 增强的订单详情（统一网关版本）
type EnhancedOrderDetail struct {
	// 🔥 订单标识信息
	PlatformOrderNo string `json:"platform_order_no"` // 平台订单号
	CustomerOrderNo string `json:"customer_order_no"` // 客户订单号
	ProviderOrderNo string `json:"provider_order_no"` // 供应商订单号
	TrackingNo      string `json:"tracking_no"`       // 运单号

	// 🔥 兼容性字段
	OrderNo string `json:"order_no"` // 主要订单号（优先显示平台订单号）

	// 基本信息
	ExpressType string `json:"express_type"` // 快递类型
	ProductType string `json:"product_type"` // 产品类型
	Provider    string `json:"provider"`     // 供应商
	Status      string `json:"status"`       // 订单状态
	StatusDesc  string `json:"status_desc"`  // 状态描述

	// 费用信息
	Price         float64 `json:"price"`          // 预收费用
	ActualFee     float64 `json:"actual_fee"`     // 实收费用
	InsuranceFee  float64 `json:"insurance_fee"`  // 保价费
	OverweightFee float64 `json:"overweight_fee"` // 超重费用
	BillingStatus string  `json:"billing_status"` // 计费状态

	// 重量体积信息
	Weight        float64 `json:"weight"`         // 下单重量
	OrderVolume   float64 `json:"order_volume"`   // 下单体积
	ActualWeight  float64 `json:"actual_weight"`  // 实际重量
	ActualVolume  float64 `json:"actual_volume"`  // 实际体积
	ChargedWeight float64 `json:"charged_weight"` // 计费重量

	// 地址信息
	SenderInfo   string `json:"sender_info"`   // 寄件人信息
	ReceiverInfo string `json:"receiver_info"` // 收件人信息
	PackageInfo  string `json:"package_info"`  // 包裹信息

	// 揽件员信息
	CourierName  string `json:"courier_name"`  // 快递员姓名
	CourierPhone string `json:"courier_phone"` // 快递员电话
	CourierCode  string `json:"courier_code"`  // 快递员工号
	StationName  string `json:"station_name"`  // 网点名称
	PickupCode   string `json:"pickup_code"`   // 取件码

	// 系统信息
	TaskId    string `json:"task_id"`    // 任务ID
	PollToken string `json:"poll_token"` // 轮询令牌
	UserID    string `json:"user_id"`    // 用户ID
	CreatedAt string `json:"created_at"` // 创建时间
	UpdatedAt string `json:"updated_at"` // 更新时间
}

// QueryMetadata 查询元数据
type QueryMetadata struct {
	QueryType       string `json:"query_type"`        // 查询类型：platform_order_no, customer_order_no, provider_order_no, tracking_no
	OriginalOrderNo string `json:"original_order_no"` // 原始查询订单号
	QueryTime       string `json:"query_time"`        // 查询时间
}

// TrackQueryResponseData 物流轨迹查询响应数据
type TrackQueryResponseData struct {
	Success bool        `json:"success"`        // 是否成功
	Code    int         `json:"code"`           // 状态码
	Message string      `json:"message"`        // 消息
	Data    interface{} `json:"data,omitempty"` // 物流轨迹数据
}

// 🎫 工单相关业务参数结构体

// CreateWorkOrderBusinessParams 创建工单业务参数
type CreateWorkOrderBusinessParams struct {
	// 工单基本信息 (必填)
	WorkOrderType int    `json:"work_order_type"` // 工单类型：1-催取件，2-重量异常，12-催派送，16-物流停滞，17-重新分配快递员，19-取消订单
	Content       string `json:"content"`         // 问题描述

	// 🔥 新增：用户自定义工单ID（可选）
	CustomerWorkOrderID *string `json:"customer_work_order_id,omitempty"` // 用户自定义工单ID

	// 订单标识 (二选一即可)
	OrderNo    *string `json:"order_no,omitempty"`    // 客户订单号
	TrackingNo *string `json:"tracking_no,omitempty"` // 运单号

	// 可选字段 (根据工单类型自动显示)
	FeedbackWeight   *float64 `json:"feedback_weight,omitempty"`   // 反馈重量（重量异常时使用）
	GoodsValue       *float64 `json:"goods_value,omitempty"`       // 商品价值
	OverweightAmount *float64 `json:"overweight_amount,omitempty"` // 超重金额
	AttachmentURLs   []string `json:"attachment_urls,omitempty"`   // 附件URL列表

	// 回调配置 (可选)
	CallbackURL        *string `json:"callback_url,omitempty"`         // 工单状态变更回调URL
	MessageCallbackURL *string `json:"message_callback_url,omitempty"` // 工单消息回调URL
}

// QueryWorkOrderBusinessParams 查询工单业务参数
type QueryWorkOrderBusinessParams struct {
	WorkOrderID string `json:"work_order_id"` // 工单ID (必填)
}

// ListWorkOrdersBusinessParams 工单列表业务参数
type ListWorkOrdersBusinessParams struct {
	// 分页参数
	Page     int `json:"page"`      // 页码 (默认1)
	PageSize int `json:"page_size"` // 每页数量 (默认20)

	// 筛选条件 (可选)
	Provider      *string `json:"provider,omitempty"`        // 供应商
	Status        *int    `json:"status,omitempty"`          // 工单状态
	WorkOrderType *int    `json:"work_order_type,omitempty"` // 工单类型
	OrderNo       *string `json:"order_no,omitempty"`        // 订单号
	TrackingNo    *string `json:"tracking_no,omitempty"`     // 运单号
	StartDate     *string `json:"start_date,omitempty"`      // 开始日期
	EndDate       *string `json:"end_date,omitempty"`        // 结束日期
}

// ReplyWorkOrderBusinessParams 回复工单业务参数
type ReplyWorkOrderBusinessParams struct {
	WorkOrderID    string   `json:"work_order_id"`             // 工单ID (必填)
	Content        string   `json:"content"`                   // 回复内容 (必填)
	AttachmentURLs []string `json:"attachment_urls,omitempty"` // 附件URL列表 (可选)
}

// DeleteWorkOrderBusinessParams 删除工单业务参数
type DeleteWorkOrderBusinessParams struct {
	WorkOrderID string `json:"work_order_id"` // 工单ID (必填)
}

// UploadWorkOrderAttachmentBusinessParams 上传工单附件业务参数
type UploadWorkOrderAttachmentBusinessParams struct {
	FileName    string `json:"file_name"`    // 文件名 (必填)
	FileContent string `json:"file_content"` // 文件内容 (Base64编码) (必填)
}

// 🎫 工单相关响应数据结构体

// CreateWorkOrderResponseData 创建工单响应数据
type CreateWorkOrderResponseData struct {
	Success             bool   `json:"success"`                // 是否成功
	Code                int    `json:"code"`                   // 状态码
	Message             string `json:"message"`                // 消息
	WorkOrderID         string `json:"work_order_id"`          // 工单ID
	CustomerWorkOrderID string `json:"customer_work_order_id"` // 🔥 新增：用户自定义工单ID
	WorkOrderType       int    `json:"work_order_type"`        // 工单类型
	Title               string `json:"title"`                  // 工单标题
	Status              int    `json:"status"`                 // 工单状态
	Provider            string `json:"provider"`               // 供应商
	RequestID           string `json:"request_id"`             // 请求ID
}

// QueryWorkOrderResponseData 查询工单响应数据
type QueryWorkOrderResponseData struct {
	Success bool        `json:"success"`        // 是否成功
	Code    int         `json:"code"`           // 状态码
	Message string      `json:"message"`        // 消息
	Data    interface{} `json:"data,omitempty"` // 工单详情数据
}

// ListWorkOrdersResponseData 工单列表响应数据
type ListWorkOrdersResponseData struct {
	Success bool        `json:"success"`        // 是否成功
	Code    int         `json:"code"`           // 状态码
	Message string      `json:"message"`        // 消息
	Data    interface{} `json:"data,omitempty"` // 工单列表数据
}

// ReplyWorkOrderResponseData 回复工单响应数据
type ReplyWorkOrderResponseData struct {
	Success   bool   `json:"success"`    // 是否成功
	Code      int    `json:"code"`       // 状态码
	Message   string `json:"message"`    // 消息
	ReplyID   string `json:"reply_id"`   // 回复ID
	RequestID string `json:"request_id"` // 请求ID
}

// DeleteWorkOrderResponseData 删除工单响应数据
type DeleteWorkOrderResponseData struct {
	Success   bool   `json:"success"`    // 是否成功
	Code      int    `json:"code"`       // 状态码
	Message   string `json:"message"`    // 消息
	RequestID string `json:"request_id"` // 请求ID
}

// UploadWorkOrderAttachmentResponseData 上传工单附件响应数据
type UploadWorkOrderAttachmentResponseData struct {
	Success      bool   `json:"success"`       // 是否成功
	Code         int    `json:"code"`          // 状态码
	Message      string `json:"message"`       // 消息
	AttachmentID string `json:"attachment_id"` // 附件ID
	FileURL      string `json:"file_url"`      // 文件URL
	RequestID    string `json:"request_id"`    // 请求ID
}

// NewUnifiedGatewayHandler 创建统一网关处理器
func NewUnifiedGatewayHandler(
	priceService service.PriceServiceInterface,
	orderHandler *OrderHandler,
	orderService *service.OrderService,
	// enhancedOrderQueryService 已删除, // 🔥 新增：增强订单查询服务参数
	workOrderService service.WorkOrderService, // 🎫 新增：工单服务参数
	failedOrderService *service.FailedOrderService, // 🔥 新增：失败订单服务参数
	providerManager *adapter.ProviderManager, // 🚀 新增：供应商管理器参数
	tokenService auth.TokenService,
	expressMappingService express.ExpressMappingService,
	expressCompanyRepository express.ExpressCompanyRepository,
	expressCompanyService express.ExpressCompanyService, // 🚀 新增：快递公司服务参数
	systemConfigService adapter.SystemConfigServiceInterface, // 🚀 新增：系统配置服务参数
	logger *zap.Logger,
) *UnifiedGatewayHandler {
	if logger == nil {
		logger, _ = zap.NewProduction()
	}

	// 🚀 创建实时查价处理器
	realtimePriceHandler := NewRealtimePriceHandler(providerManager, expressMappingService, expressCompanyService, systemConfigService, logger)

	return &UnifiedGatewayHandler{
		priceService: priceService,
		orderHandler: orderHandler,
		orderService: orderService,
		// enhancedOrderQueryService 已删除
		workOrderService:         workOrderService,     // 🎫 注入工单服务
		failedOrderService:       failedOrderService,   // 🔥 注入失败订单服务
		realtimePriceHandler:     realtimePriceHandler, // 🚀 注入实时查价处理器
		tokenService:             tokenService,
		expressMappingService:    expressMappingService,
		expressCompanyRepository: expressCompanyRepository,
		expressCompanyService:    expressCompanyService, // 🚀 注入快递公司服务
		systemConfigService:      systemConfigService,   // 🚀 注入系统配置服务
		logger:                   logger,
	}
}

// SetUserRepository 注入用户仓储（便于在main或测试时灵活设置）
func (h *UnifiedGatewayHandler) SetUserRepository(repo user.UserRepository) {
	h.userRepo = repo
}

// Execute 统一网关执行入口
// 🚀 单一URL处理所有API请求，根据clientType和apiMethod路由到不同的处理逻辑
func (h *UnifiedGatewayHandler) Execute(c *gin.Context) {
	var req UnifiedRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.sendError(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	// 🔐 根据客户端类型进行不同的认证策略
	switch strings.ToLower(req.ClientType) {
	case "web", "admin":
		// 🌐 Web客户端：仅需JWT Token认证
		if err := h.authenticateWebClient(c, &req); err != nil {
			h.sendError(c, http.StatusUnauthorized, "Authentication failed", err.Error())
			return
		}
	case "api", "":
		// 🔒 API客户端：签名验证已由签名中间件处理
		// 这里只需要验证请求参数的完整性
		if err := h.validateAPIClientRequest(c, &req); err != nil {
			h.sendError(c, http.StatusUnauthorized, "Authentication failed", err.Error())
			return
		}
	default:
		h.sendError(c, http.StatusBadRequest, "Invalid client type", "clientType must be 'web', 'admin', or 'api'")
		return
	}

	// 🚀 根据apiMethod路由到具体的业务处理
	switch req.APIMethod {
	// 📊 价格查询相关
	case "QUERY_PRICE":
		h.handleQueryPrice(c, &req)
	case "SMART_PRICE_QUERY":
		// 🧠 智能价格查询：根据快递公司配置自动选择接口
		h.handleSmartPriceQuery(c, &req)

	// 🚀 实时查价
	case "QUERY_REALTIME_PRICE":
		h.handleQueryRealtimePrice(c, &req)

	// 📦 订单创建相关
	case "CREATE_ORDER":
		h.handleCreateOrder(c, &req)

	// 🚫 订单取消相关
	case "CANCEL_ORDER":
		h.handleCancelOrder(c, &req)

	// 🔍 订单查询相关
	case "QUERY_ORDER":
		h.handleQueryOrder(c, &req)

	// 📦 物流轨迹查询相关
	case "QUERY_TRACK":
		h.handleQueryTrack(c, &req)

	// 🎫 工单管理相关
	case "CREATE_WORKORDER":
		h.handleCreateWorkOrder(c, &req)
	case "QUERY_WORKORDER":
		h.handleQueryWorkOrder(c, &req)
	case "LIST_WORKORDERS":
		h.handleListWorkOrders(c, &req)
	case "REPLY_WORKORDER":
		h.handleReplyWorkOrder(c, &req)
	case "DELETE_WORKORDER":
		h.handleDeleteWorkOrder(c, &req)
	case "UPLOAD_WORKORDER_ATTACHMENT":
		h.handleUploadWorkOrderAttachment(c, &req)

	// 👤 用户相关（仅Web客户端）
	case "USER_LOGIN":
		h.sendError(c, http.StatusNotImplemented, "Method not implemented", "USER_LOGIN will be implemented soon")
	case "USER_PROFILE":
		h.sendError(c, http.StatusNotImplemented, "Method not implemented", "USER_PROFILE will be implemented soon")
	case "ADMIN_LOGIN":
		h.sendError(c, http.StatusNotImplemented, "Method not implemented", "ADMIN_LOGIN will be implemented soon")

	default:
		h.sendError(c, http.StatusBadRequest, "Unsupported API method", "apiMethod: "+req.APIMethod)
	}
}

// authenticateWebClient Web客户端认证（仅JWT Token）
func (h *UnifiedGatewayHandler) authenticateWebClient(c *gin.Context, req *UnifiedRequest) error {
	// 🌐 Web客户端使用JWT Token认证
	if req.AccessToken == "" {
		return fmt.Errorf("access_token is required for web clients")
	}

	// 验证JWT Token
	claims, err := h.tokenService.ValidateToken(req.AccessToken)
	if err != nil {
		return fmt.Errorf("invalid access token: %v", err)
	}

	// 将用户信息存储到上下文
	c.Set("user_claims", claims)
	c.Set("client_type", "web")

	// 🔥 修复：正确设置用户ID到上下文中
	// 优先使用Subject字段（用户令牌），其次使用ClientID字段（客户端令牌）
	var userID string
	if claims.Subject != "" {
		userID = claims.Subject
		h.logger.Debug("Web客户端认证：从Subject获取用户ID",
			zap.String("user_id", userID),
			zap.String("client_id", claims.ClientID))
	} else if claims.ClientID != "" {
		userID = claims.ClientID
		h.logger.Debug("Web客户端认证：从ClientID获取用户ID",
			zap.String("user_id", userID),
			zap.String("client_id", claims.ClientID))
	} else {
		return fmt.Errorf("JWT token does not contain valid user identification")
	}

	// 设置用户ID到上下文（使用多个键名确保兼容性）
	c.Set("user_id", userID)
	c.Set("userID", userID)
	c.Set("client_id", claims.ClientID)

	h.logger.Info("Web客户端认证成功",
		zap.String("user_id", userID),
		zap.String("client_id", claims.ClientID),
		zap.Strings("scopes", claims.Scopes))

	return nil
}

// validateAPIClientRequest 验证API客户端请求参数
func (h *UnifiedGatewayHandler) validateAPIClientRequest(c *gin.Context, req *UnifiedRequest) error {
	// 🔒 API客户端需要完整的签名参数
	if req.Username == "" || req.Timestamp == "" || req.Sign == "" {
		return fmt.Errorf("username, timestamp, and sign are required for API clients")
	}

	// 🔒 签名验证已由签名中间件处理
	// 如果请求到达这里，说明签名验证已经通过
	c.Set("client_id", req.Username)
	c.Set("client_type", "api")
	return nil
}

// sendSuccess 发送成功响应
func (h *UnifiedGatewayHandler) sendSuccess(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, UnifiedResponse{
		Code:    200,
		Message: "操作成功",
		Data:    data,
		Success: true,
	})
}

// sendError 发送错误响应
func (h *UnifiedGatewayHandler) sendError(c *gin.Context, statusCode int, message, detail string) {
	c.JSON(statusCode, UnifiedResponse{
		Code:    statusCode,
		Message: message,
		Data:    detail,
		Success: false,
	})
}

// categorizeError 智能分类错误，返回合适的HTTP状态码和用户友好的消息
func (h *UnifiedGatewayHandler) categorizeError(err error) (int, string) {
	if err == nil {
		return http.StatusOK, "Success"
	}

	// 处理特定错误类型
	switch err.(type) {
	case *model.ProviderNotSupportedError:
		return http.StatusBadRequest, "该快递公司不支持当前线路，请选择其他快递公司"
	case *model.CapacityError:
		return http.StatusBadRequest, "当前线路暂时不可用，请选择其他快递公司或稍后重试"
	case *model.NetworkError:
		return http.StatusServiceUnavailable, "网络连接异常，请稍后重试"
	}

	errMsg := err.Error()

	// 🔧 运力异常 - 直接返回供应商原始错误信息
	if strings.Contains(errMsg, "运力异常") ||
		strings.Contains(errMsg, "内部接口服务失败") ||
		strings.Contains(errMsg, "网点已关停") ||
		strings.Contains(errMsg, "白马取号异常") {
		// 直接返回原始错误信息，不做任何转换
		return http.StatusBadRequest, errMsg
	}

	// 🔧 供应商不支持的情况 - 直接返回原始错误信息
	if strings.Contains(errMsg, "供应商不支持") ||
		strings.Contains(errMsg, "不支持快递公司") ||
		strings.Contains(errMsg, "当前线路暂未开放") ||
		strings.Contains(errMsg, "该区域暂时不开放") ||
		strings.Contains(errMsg, "大于2.49公斤") {
		return http.StatusBadRequest, errMsg
	}

	// 🔧 价格验证失败 - 业务逻辑错误，需要用户重新查价
	if strings.Contains(errMsg, "价格已变动") ||
		strings.Contains(errMsg, "价格验证失败") ||
		strings.Contains(errMsg, "获取实时价格失败") {
		return http.StatusBadRequest, "价格已发生变动，请重新查询最新价格"
	}

	// 🔧 参数验证错误
	if strings.Contains(errMsg, "参数") ||
		strings.Contains(errMsg, "必填") ||
		strings.Contains(errMsg, "格式") ||
		strings.Contains(errMsg, "无效") {
		return http.StatusBadRequest, "请求参数错误"
	}

	// 🔧 认证授权错误
	if strings.Contains(errMsg, "认证") ||
		strings.Contains(errMsg, "授权") ||
		strings.Contains(errMsg, "权限") ||
		strings.Contains(errMsg, "token") {
		return http.StatusUnauthorized, "认证失败"
	}

	// 🔧 网络或外部服务错误 - 直接返回原始错误信息
	if strings.Contains(errMsg, "网络") ||
		strings.Contains(errMsg, "超时") ||
		strings.Contains(errMsg, "连接") ||
		strings.Contains(errMsg, "API调用失败") {
		return http.StatusServiceUnavailable, errMsg
	}

	// 🔧 创建订单失败的通用处理 - 直接返回原始错误信息
	if strings.Contains(errMsg, "创建订单失败") {
		return http.StatusBadRequest, errMsg
	}

	// 🔧 默认为内部服务器错误
	h.logger.Warn("未分类的错误", zap.String("error", errMsg))
	return http.StatusInternalServerError, "服务器处理失败，请稍后重试"
}

// handleQueryPrice 处理价格查询
func (h *UnifiedGatewayHandler) handleQueryPrice(c *gin.Context, req *UnifiedRequest) {
	// 解析业务参数
	var priceReq SimplePriceRequest
	if err := h.parseBusinessParams(req.BusinessParams, &priceReq); err != nil {
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 验证必填参数
	if err := h.validateSimplePriceRequest(priceReq); err != nil {
		h.sendError(c, http.StatusBadRequest, "Invalid parameters", err.Error())
		return
	}

	// 转换为内部请求格式
	internalReq := h.convertToInternalPriceRequest(priceReq)

	// 直接调用价格查询服务
	resp, err := h.priceService.QueryPrice(c.Request.Context(), internalReq)
	if err != nil {
		// 🔧 智能错误处理：区分不同类型的错误
		statusCode, message := h.categorizeError(err)
		h.sendError(c, statusCode, message, err.Error())
		return
	}

	// 创建SimplePriceHandler来复用转换逻辑
	simplePriceHandler := NewSimplePriceHandler(
		h.priceService,
		h.expressMappingService,
		h.expressCompanyRepository,
	)

	// 转换为简化响应格式
	simpleResp := simplePriceHandler.ConvertToSimplePriceResponse(resp, &priceReq)

	// 发送成功响应
	h.sendSuccess(c, simpleResp)
}

// handleSmartPriceQuery 智能价格查询：根据快递公司配置自动选择接口
func (h *UnifiedGatewayHandler) handleSmartPriceQuery(c *gin.Context, req *UnifiedRequest) {
	// 解析业务参数
	var priceReq SimplePriceRequest
	if err := h.parseBusinessParams(req.BusinessParams, &priceReq); err != nil {
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 验证必填参数
	if err := h.validateSimplePriceRequest(priceReq); err != nil {
		h.sendError(c, http.StatusBadRequest, "Invalid parameters", err.Error())
		return
	}

	// 🧠 智能路由逻辑：根据快递公司配置决定使用哪个接口
	shouldUseJDInterface, err := h.shouldUseJDInterface(c.Request.Context(), &priceReq)
	if err != nil {
		h.logger.Error("智能接口选择失败", zap.Error(err))
		h.sendError(c, http.StatusInternalServerError, "Interface selection failed", err.Error())
		return
	}

	if shouldUseJDInterface {
		// 使用实时查价接口
		h.logger.Info("智能路由：使用实时价格查询接口",
			zap.String("express_code", priceReq.ExpressCode),
			zap.String("reason", "快递公司配置为dedicated接口"))
		h.handleRealtimePriceQueryFromSimpleRequest(c, req, &priceReq)
	} else {
		// 使用标准价格查询接口
		h.logger.Info("智能路由：使用标准价格查询接口",
			zap.String("express_code", priceReq.ExpressCode),
			zap.String("reason", "快递公司配置为unified接口或未指定快递公司"))
		h.handleQueryPriceFromSimpleRequest(c, req, &priceReq)
	}
}

// shouldUseJDInterface 判断是否应该使用实时查价接口（重命名但保持向下兼容）
func (h *UnifiedGatewayHandler) shouldUseJDInterface(ctx context.Context, priceReq *SimplePriceRequest) (bool, error) {
	// 如果没有指定快递公司，使用标准接口
	if priceReq.ExpressCode == "" {
		return false, nil
	}

	// 🚀 新增：优先使用新的接口分配器
	if h.expressCompanyService != nil {
		interfaceType, err := h.expressCompanyService.GetPriceInterface(ctx, priceReq.ExpressCode)
		if err != nil {
			h.logger.Warn("使用接口分配器获取接口类型失败，降级到旧逻辑",
				zap.String("express_code", priceReq.ExpressCode),
				zap.Error(err))
			// 降级到旧的获取方式
			return h.shouldUseJDInterfaceLegacy(ctx, priceReq)
		}

		// 根据新的接口类型决定使用哪个接口
		useRealtimeInterface := interfaceType == "QUERY_REALTIME_PRICE"

		h.logger.Debug("接口分配器返回接口类型",
			zap.String("express_code", priceReq.ExpressCode),
			zap.String("interface_type", string(interfaceType)),
			zap.Bool("use_realtime_interface", useRealtimeInterface))

		return useRealtimeInterface, nil
	}

	// 降级到旧的获取方式
	return h.shouldUseJDInterfaceLegacy(ctx, priceReq)
}

// shouldUseJDInterfaceLegacy 旧的接口选择逻辑（向下兼容）
func (h *UnifiedGatewayHandler) shouldUseJDInterfaceLegacy(ctx context.Context, priceReq *SimplePriceRequest) (bool, error) {
	// 查询快递公司的接口类型配置
	interfaceType, err := h.getExpressCompanyInterfaceType(ctx, priceReq.ExpressCode)
	if err != nil {
		h.logger.Warn("获取快递公司接口类型失败，使用标准接口",
			zap.String("express_code", priceReq.ExpressCode),
			zap.Error(err))
		return false, nil // 出错时默认使用标准接口
	}

	// 根据配置决定使用哪个接口
	return interfaceType == "dedicated", nil
}

// getExpressCompanyInterfaceType 获取快递公司的接口类型配置
func (h *UnifiedGatewayHandler) getExpressCompanyInterfaceType(ctx context.Context, expressCode string) (string, error) {
	// 查询快递公司信息
	company, err := h.expressCompanyRepository.GetCompanyByCode(expressCode)
	if err != nil {
		return "", fmt.Errorf("查询快递公司失败: %w", err)
	}
	if company == nil {
		return "", fmt.Errorf("快递公司不存在: %s", expressCode)
	}

	// 查询快递公司的interface_type配置
	configs, err := h.expressCompanyRepository.GetConfigsByCompany(company.ID)
	if err != nil {
		return "", fmt.Errorf("查询快递公司配置失败: %w", err)
	}

	// 查找interface_type配置
	for _, config := range configs {
		if config.ConfigKey == "interface_type" {
			return config.ConfigValue, nil
		}
	}

	// 默认使用统一接口
	return "unified", nil
}

// handleQueryPriceFromSimpleRequest 从简单价格请求调用标准价格查询
func (h *UnifiedGatewayHandler) handleQueryPriceFromSimpleRequest(c *gin.Context, req *UnifiedRequest, priceReq *SimplePriceRequest) {
	// 将SimplePriceRequest转换为标准的价格查询请求
	standardReq := &UnifiedRequest{
		APIMethod:      "QUERY_PRICE",
		BusinessParams: req.BusinessParams, // 直接使用原始参数
	}

	// 调用标准价格查询处理器
	h.handleQueryPrice(c, standardReq)
}

// handleRealtimePriceQueryFromSimpleRequest 从简单价格请求调用实时价格查询
func (h *UnifiedGatewayHandler) handleRealtimePriceQueryFromSimpleRequest(c *gin.Context, req *UnifiedRequest, priceReq *SimplePriceRequest) {
	// 将SimplePriceRequest转换为实时价格查询请求
	realtimeReq := &UnifiedRequest{
		APIMethod:      "QUERY_REALTIME_PRICE",
		BusinessParams: req.BusinessParams, // 直接使用原始参数
	}

	// 调用实时价格查询处理器
	h.handleQueryRealtimePrice(c, realtimeReq)
}

// parseBusinessParams 解析业务参数
func (h *UnifiedGatewayHandler) parseBusinessParams(params interface{}, target interface{}) error {
	// 将interface{}转换为JSON，再解析到目标结构体
	jsonData, err := json.Marshal(params)
	if err != nil {
		return err
	}

	return json.Unmarshal(jsonData, target)
}

// validateSimplePriceRequest 验证价格查询请求参数
func (h *UnifiedGatewayHandler) validateSimplePriceRequest(req SimplePriceRequest) error {
	// 兼容两种写法：扁平字段或嵌套 sender/receiver
	fromProvince := req.FromProvince
	fromCity := req.FromCity
	toProvince := req.ToProvince
	toCity := req.ToCity

	if fromProvince == "" {
		fromProvince = req.Sender.Province
	}
	if fromCity == "" {
		fromCity = req.Sender.City
	}
	if toProvince == "" {
		toProvince = req.Receiver.Province
	}
	if toCity == "" {
		toCity = req.Receiver.City
	}

	if fromProvince == "" || fromCity == "" {
		return fmt.Errorf("寄件人省市不能为空")
	}

	if toProvince == "" || toCity == "" {
		return fmt.Errorf("收件人省市不能为空")
	}

	if req.Weight <= 0 {
		return fmt.Errorf("包裹重量必须大于0")
	}

	return nil
}

// convertToInternalPriceRequest 转换为内部请求格式
func (h *UnifiedGatewayHandler) convertToInternalPriceRequest(req SimplePriceRequest) *model.PriceRequest {
	// 导入model包
	// 设置默认值
	quantity := req.Quantity
	if quantity <= 0 {
		quantity = 1
	}

	goodsName := req.GoodsName
	if goodsName == "" {
		goodsName = "物品"
	}

	// 优先使用长宽高计算体积
	volume := req.Volume
	if req.Length > 0 && req.Width > 0 && req.Height > 0 {
		// 长宽高单位是cm，需要转换为m³
		volumeCm3 := req.Length * req.Width * req.Height
		volume = volumeCm3 / 1000000 // cm³ → m³
	}

	// 🚀 新增：根据是否指定快递公司代码决定查询模式
	queryAllCompanies := req.ExpressCode == ""
	expressType := req.ExpressCode

	// 兼容两种写法
	fromProvince := req.FromProvince
	fromCity := req.FromCity
	fromDistrict := req.FromDistrict
	toProvince := req.ToProvince
	toCity := req.ToCity
	toDistrict := req.ToDistrict

	if fromProvince == "" {
		fromProvince = req.Sender.Province
		fromCity = req.Sender.City
		fromDistrict = req.Sender.District
	}
	if toProvince == "" {
		toProvince = req.Receiver.Province
		toCity = req.Receiver.City
		toDistrict = req.Receiver.District
	}

	// 生成唯一的客户订单号
	customerOrderNo := "GATEWAY_" + fmt.Sprintf("%d", util.NowBeijing().UnixNano())

	return &model.PriceRequest{
		CustomerOrderNo:   customerOrderNo,
		ExpressType:       expressType,       // 设置快递公司代码
		QueryAllCompanies: queryAllCompanies, // 根据是否指定快递公司决定查询模式
		Sender: model.SenderInfo{
			Province: fromProvince,
			City:     fromCity,
			District: fromDistrict,
			Name:     "寄件人",
			Mobile:   "13800000000",
			Address:  fromDistrict + "某地",
		},
		Receiver: model.ReceiverInfo{
			Province: toProvince,
			City:     toCity,
			District: toDistrict,
			Name:     "收件人",
			Mobile:   "13900000000",
			Address:  toDistrict + "某地",
		},
		Package: model.PackageInfo{
			Weight:    req.Weight,
			Length:    req.Length,
			Width:     req.Width,
			Height:    req.Height,
			Volume:    volume,
			Quantity:  quantity,
			GoodsName: goodsName,
		},
		PayMethod: req.PayMethod,
	}
}

// handleCreateOrder 处理订单创建请求
func (h *UnifiedGatewayHandler) handleCreateOrder(c *gin.Context, req *UnifiedRequest) {
	// 🚀 新增：记录统一网关下单请求开始
	h.logger.Info("统一网关下单请求开始",
		zap.String("api_method", req.APIMethod),
		zap.String("username", req.Username),
		zap.String("client_type", req.ClientType))

	// 🔄 解析业务参数
	var params CreateOrderBusinessParams
	if err := h.parseBusinessParams(req.BusinessParams, &params); err != nil {
		// 🚀 新增：记录参数解析失败
		h.logger.Error("统一网关下单参数解析失败",
			zap.String("username", req.Username),
			zap.String("client_type", req.ClientType),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 🔥 调试：记录解析后的预约时间参数
	h.logger.Info("统一网关：参数解析完成",
		zap.String("pickup_start_time", params.PickupStartTime),
		zap.String("pickup_end_time", params.PickupEndTime),
		zap.Bool("start_time_empty", params.PickupStartTime == ""),
		zap.Bool("end_time_empty", params.PickupEndTime == ""))

	// 🔐 验证必填参数
	if err := h.validateCreateOrderParams(&params); err != nil {
		// 🚀 新增：记录参数验证失败，包含更多调试信息帮助诊断
		h.logger.Error("统一网关下单参数验证失败",
			zap.String("username", req.Username),
			zap.String("order_code", params.OrderCode),
			zap.String("user_id", params.UserID),
			zap.String("receiver_name", params.ReceiverName),
			zap.String("receiver_mobile", params.ReceiverMobile), // 记录实际接收到的值
			zap.String("sender_mobile", params.SenderMobile),
			zap.String("receiver_province", params.ReceiverProvince),
			zap.String("receiver_city", params.ReceiverCity),
			zap.Error(err))

		// 🔥 新增：记录参数验证失败到数据库
		h.recordParameterValidationFailure(c.Request.Context(), &params, req, err)

		h.sendError(c, http.StatusBadRequest, "Parameter validation failed", err.Error())
		return
	}

	// 🚀 新增：记录关键参数信息
	h.logger.Info("统一网关下单参数验证成功",
		zap.String("username", req.Username),
		zap.String("order_code", params.OrderCode),
		zap.String("user_id", params.UserID),
		zap.String("sender_province", params.SenderProvince),
		zap.String("sender_city", params.SenderCity),
		zap.String("receiver_province", params.ReceiverProvince),
		zap.String("receiver_city", params.ReceiverCity),
		zap.Float64("weight", params.Weight),
		zap.Float64("expected_price", params.ExpectedPrice))

	// 🔄 转换为内部订单请求格式
	simpleOrderReq := h.convertToSimpleOrderRequest(&params)

	// 🚀 调用现有的订单创建逻辑
	orderResp, err := h.createOrderInternal(c, simpleOrderReq)
	if err != nil {
		// 🚀 新增：记录订单创建失败
		h.logger.Error("统一网关下单创建失败",
			zap.String("username", req.Username),
			zap.String("order_code", params.OrderCode),
			zap.String("user_id", params.UserID),
			zap.Error(err))

		// 🔥 修复：移除重复的失败订单记录创建
		// 失败订单记录由订单服务统一处理，避免重复创建
		// h.createFailedOrderRecordSync(c.Request.Context(), &params, err) // 已移除

		// 🔧 智能错误处理：区分不同类型的错误
		statusCode, message := h.categorizeError(err)
		h.sendError(c, statusCode, message, err.Error())
		return
	}

	// 🚀 新增：记录订单创建成功
	h.logger.Info("统一网关下单创建成功",
		zap.String("username", req.Username),
		zap.String("order_code", params.OrderCode),
		zap.String("user_id", params.UserID),
		zap.String("order_no", orderResp.OrderNo),
		zap.String("waybill_no", orderResp.WaybillNo),
		zap.Bool("success", orderResp.Success))

	// 📤 返回统一格式响应
	h.sendSuccess(c, orderResp)
}

// handleCancelOrder 处理取消订单请求
func (h *UnifiedGatewayHandler) handleCancelOrder(c *gin.Context, req *UnifiedRequest) {
	// 🔄 解析业务参数
	var params CancelOrderBusinessParams
	if err := h.parseBusinessParams(req.BusinessParams, &params); err != nil {
		h.logger.Error("取消订单参数解析失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 🔐 验证必填参数
	if err := h.validateCancelOrderParams(&params); err != nil {
		h.logger.Warn("取消订单参数验证失败",
			zap.String("order_no", params.OrderNo),
			zap.String("tracking_no", params.TrackingNo),
			zap.String("platform_order_no", params.PlatformOrderNo),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Parameter validation failed", err.Error())
		return
	}

	// 🔐 获取用户ID（从认证上下文中获取）
	userID, err := h.getUserIDFromContext(c, req)
	if err != nil {
		h.logger.Error("获取用户身份失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusUnauthorized, "Authentication failed", err.Error())
		return
	}

	// 📊 记录取消订单请求
	h.logger.Info("处理取消订单请求",
		zap.String("order_no", params.OrderNo),
		zap.String("tracking_no", params.TrackingNo),
		zap.String("platform_order_no", params.PlatformOrderNo),
		zap.String("reason", params.Reason),
		zap.String("user_id", userID),
		zap.String("client_type", req.ClientType))

	// 🚀 调用现有的取消订单逻辑
	cancelResp, err := h.cancelOrderInternal(c, &params, userID)
	if err != nil {
		h.logger.Error("取消订单处理失败",
			zap.String("order_no", params.OrderNo),
			zap.String("platform_order_no", params.PlatformOrderNo),
			zap.String("user_id", userID),
			zap.Error(err))
		// 🔧 智能错误处理：区分不同类型的错误
		statusCode, message := h.categorizeError(err)
		h.sendError(c, statusCode, message, err.Error())
		return
	}

	// 📊 记录取消订单成功
	h.logger.Info("取消订单处理成功",
		zap.String("order_no", params.OrderNo),
		zap.String("platform_order_no", params.PlatformOrderNo),
		zap.String("user_id", userID),
		zap.Bool("success", cancelResp.Success),
		zap.String("status", cancelResp.Status),
		zap.String("request_id", cancelResp.RequestID))

	// 📤 返回统一格式响应
	h.sendSuccess(c, cancelResp)
}

// recordParameterValidationFailure 记录参数验证失败到数据库
func (h *UnifiedGatewayHandler) recordParameterValidationFailure(ctx context.Context, params *CreateOrderBusinessParams, req *UnifiedRequest, validationErr error) {
	// 如果没有订单服务，则跳过
	if h.orderService == nil {
		return
	}

	// 生成客户订单号（如果没有提供）
	customerOrderNo := params.CustomerOrderNo
	if customerOrderNo == "" {
		// 尝试从订单代码解析
		if params.OrderCode != "" {
			customerOrderNo = fmt.Sprintf("VALIDATION_FAILED_%d", util.NowBeijing().Unix())
		} else {
			customerOrderNo = fmt.Sprintf("UNKNOWN_ORDER_%d", util.NowBeijing().Unix())
		}
	}

	// 构建请求数据
	requestData := map[string]interface{}{
		"api_method":       req.APIMethod,
		"client_type":      req.ClientType,
		"username":         req.Username,
		"business_params":  params,
		"validation_error": validationErr.Error(),
	}

	// 创建下单尝试记录
	attemptReq := &model.OrderAttemptRequest{
		CustomerOrderNo:  customerOrderNo,
		UserID:           params.UserID,
		Provider:         "unknown", // 参数验证阶段还未确定供应商
		ExpressType:      "unknown", // 参数验证阶段还未确定快递类型
		AttemptStage:     "PARAMETER_VALIDATION_FAILED",
		StageDescription: "统一网关参数验证失败",
		Success:          false,
		ErrorMessage:     validationErr.Error(),
		ErrorCode:        "VALIDATION_ERROR",
		RequestData:      requestData,
		ResponseData:     nil,
		ProcessingTimeMs: 0,
		RetryCount:       0,
		Metadata: map[string]interface{}{
			"gateway_type":     "unified_gateway",
			"validation_stage": "create_order_params",
		},
	}

	// 异步记录，不影响主流程
	go func() {
		if err := h.orderService.RecordOrderAttempt(context.Background(), attemptReq); err != nil {
			h.logger.Warn("记录参数验证失败到数据库失败",
				zap.String("customer_order_no", customerOrderNo),
				zap.Error(err))
		}
	}()
}

// validateCreateOrderParams 验证订单创建参数
func (h *UnifiedGatewayHandler) validateCreateOrderParams(params *CreateOrderBusinessParams) error {
	// 验证必填字段
	if params.UserID == "" {
		return fmt.Errorf("user_id is required")
	}
	if params.OrderCode == "" {
		return fmt.Errorf("order_code is required")
	}

	// 验证寄件人信息
	if params.SenderName == "" {
		return fmt.Errorf("sender_name is required")
	}
	if params.SenderMobile == "" {
		return fmt.Errorf("sender_mobile is required")
	}
	if params.SenderProvince == "" {
		return fmt.Errorf("sender_province is required")
	}
	if params.SenderCity == "" {
		return fmt.Errorf("sender_city is required")
	}
	if params.SenderDistrict == "" {
		return fmt.Errorf("sender_district is required")
	}
	if params.SenderAddress == "" {
		return fmt.Errorf("sender_address is required")
	}

	// 验证收件人信息
	if params.ReceiverName == "" {
		return fmt.Errorf("receiver_name is required")
	}
	if params.ReceiverMobile == "" {
		return fmt.Errorf("receiver_mobile is required")
	}
	if params.ReceiverProvince == "" {
		return fmt.Errorf("receiver_province is required")
	}
	if params.ReceiverCity == "" {
		return fmt.Errorf("receiver_city is required")
	}
	if params.ReceiverDistrict == "" {
		return fmt.Errorf("receiver_district is required")
	}
	if params.ReceiverAddress == "" {
		return fmt.Errorf("receiver_address is required")
	}

	// 验证包裹信息
	if params.Weight <= 0 {
		return fmt.Errorf("weight must be greater than 0")
	}

	return nil
}

// validateCancelOrderParams 验证取消订单参数
func (h *UnifiedGatewayHandler) validateCancelOrderParams(params *CancelOrderBusinessParams) error {
	// 🔐 企业级参数验证：至少需要一个查询条件
	hasQueryCondition := params.OrderNo != "" || params.TrackingNo != "" || params.PlatformOrderNo != ""

	if !hasQueryCondition {
		return fmt.Errorf("至少需要提供一个查询条件：order_no、tracking_no或platform_order_no")
	}

	// 🔐 验证取消原因
	if params.Reason == "" {
		return fmt.Errorf("reason is required")
	}

	// 🔐 参数长度验证
	orderNoFields := map[string]string{
		"order_no":          params.OrderNo,
		"tracking_no":       params.TrackingNo,
		"platform_order_no": params.PlatformOrderNo,
	}

	for fieldName, fieldValue := range orderNoFields {
		if fieldValue != "" && (len(fieldValue) < 3 || len(fieldValue) > 50) {
			return fmt.Errorf("%s长度应在3-50位之间", fieldName)
		}
	}

	return nil
}

// convertToSimpleOrderRequest 转换为SimpleOrderRequest
func (h *UnifiedGatewayHandler) convertToSimpleOrderRequest(params *CreateOrderBusinessParams) *SimpleOrderRequest {
	// 设置默认值
	quantity := params.Quantity
	if quantity <= 0 {
		quantity = 1
	}

	goodsName := params.GoodsName
	if goodsName == "" {
		goodsName = "物品"
	}

	return &SimpleOrderRequest{
		UserID:          params.UserID,
		CustomerOrderNo: params.CustomerOrderNo, // 🔥 新增：传递客户订单号
		OrderCode:       params.OrderCode,

		// 寄件人信息
		SenderName:     params.SenderName,
		SenderMobile:   params.SenderMobile,
		SenderProvince: params.SenderProvince,
		SenderCity:     params.SenderCity,
		SenderDistrict: params.SenderDistrict,
		SenderAddress:  params.SenderAddress,

		// 收件人信息
		ReceiverName:     params.ReceiverName,
		ReceiverMobile:   params.ReceiverMobile,
		ReceiverProvince: params.ReceiverProvince,
		ReceiverCity:     params.ReceiverCity,
		ReceiverDistrict: params.ReceiverDistrict,
		ReceiverAddress:  params.ReceiverAddress,

		// 包裹信息
		Weight:    params.Weight,
		Volume:    params.Volume,
		Length:    params.Length,
		Width:     params.Width,
		Height:    params.Height,
		Quantity:  quantity,
		GoodsName: goodsName,

		// 其他信息
		PayMethod:     params.PayMethod,
		Remark:        params.Remark,
		InsureValue:   params.InsureValue,
		ExpectedPrice: params.ExpectedPrice,

		// 预约时间相关字段
		PickupStartTime: params.PickupStartTime,
		PickupEndTime:   params.PickupEndTime,

		// 🔥 新增：菜鸟裹裹专用字段
		OutOrderId: params.OutOrderId,
	}
}

// createOrderInternal 内部订单创建逻辑
func (h *UnifiedGatewayHandler) createOrderInternal(c *gin.Context, req *SimpleOrderRequest) (*CreateOrderResponseData, error) {
	// 🚀 复用现有的OrderHandler逻辑
	if h.orderHandler == nil {
		return nil, fmt.Errorf("order handler not initialized")
	}

	// 调用OrderHandler的CreateSimpleOrder逻辑，但捕获结果而不是写入HTTP响应
	orderResult, err := h.callOrderHandlerCreateLogic(c, req)
	if err != nil {
		// 🔥 修复：直接返回错误，让上层处理
		return nil, err
	}

	// 转换为统一响应格式
	return &CreateOrderResponseData{
		Success:         orderResult.Success,
		Code:            orderResult.Code,
		Message:         orderResult.Message,
		PlatformOrderNo: orderResult.PlatformOrderNo,                                                   // 🔥 平台订单号
		CustomerOrderNo: orderResult.CustomerOrderNo,                                                   // 🔥 客户订单号
		OrderNo:         getPrimaryOrderNoFromResult(orderResult.PlatformOrderNo, orderResult.OrderNo), // 🔥 主要展示字段：平台订单号
		WaybillNo:       orderResult.WaybillNo,
		ExpressCode:     orderResult.ExpressCode,
		ExpressName:     orderResult.ExpressName,
		Price:           orderResult.Price,
		// 注意：不再返回供应商内部订单号，这是系统内部使用的
	}, nil
}

// 🗑️ convertToGatewayOrderDetail 已删除 - 垃圾方法，引用了不存在的类型

// getPrimaryOrderNoFromResult 获取主要订单号（优先返回平台订单号）
func getPrimaryOrderNoFromResult(platformOrderNo, providerOrderNo string) string {
	if platformOrderNo != "" {
		return platformOrderNo
	}
	return providerOrderNo
}

// callOrderHandlerCreateLogic 调用OrderHandler的创建逻辑并返回结果
func (h *UnifiedGatewayHandler) callOrderHandlerCreateLogic(c *gin.Context, req *SimpleOrderRequest) (*SimpleOrderResponse, error) {
	// 🚀 新增：记录订单代码解析开始
	h.logger.Info("统一网关开始解析订单代码",
		zap.String("order_code", req.OrderCode),
		zap.String("user_id", req.UserID))

	// 🔄 解析订单代码
	expressCode, originalCode, provider, channelID, productCode, _, err := h.parseOrderCodeWithValidation(req.OrderCode)
	if err != nil {
		// 🚀 新增：记录订单代码解析失败
		h.logger.Error("统一网关订单代码解析失败",
			zap.String("order_code", req.OrderCode),
			zap.String("user_id", req.UserID),
			zap.Error(err))
		return &SimpleOrderResponse{
			Success: false,
			Code:    400,
			Message: fmt.Sprintf("订单代码解析失败: %s", err.Error()),
		}, nil
	}

	// 🚀 新增：记录订单代码解析成功
	h.logger.Info("统一网关订单代码解析成功",
		zap.String("order_code", req.OrderCode),
		zap.String("user_id", req.UserID),
		zap.String("express_code", expressCode),
		zap.String("original_code", originalCode),
		zap.String("provider", provider),
		zap.String("channel_id", channelID),
		zap.String("product_code", productCode))

	// 🔄 转换为内部订单请求格式
	orderReq, err := h.convertToInternalOrderRequest(c.Request.Context(), *req, expressCode, originalCode, provider, channelID, productCode, req.UserID)
	if err != nil {
		// 🚀 新增：记录请求转换失败
		h.logger.Error("统一网关请求转换失败",
			zap.String("order_code", req.OrderCode),
			zap.String("user_id", req.UserID),
			zap.String("provider", provider),
			zap.Error(err))
		return &SimpleOrderResponse{
			Success: false,
			Code:    400,
			Message: fmt.Sprintf("请求转换失败: %s", err.Error()),
		}, nil
	}

	// 🚀 新增：记录开始调用订单服务
	h.logger.Info("统一网关开始调用订单服务",
		zap.String("customer_order_no", orderReq.CustomerOrderNo),
		zap.String("user_id", req.UserID),
		zap.String("provider", provider),
		zap.String("express_type", orderReq.ExpressType))

	// 🚀 调用订单服务创建订单
	orderResp, err := h.orderService.CreateOrder(c.Request.Context(), orderReq)
	if err != nil {
		// 🚀 新增：记录订单服务调用失败
		h.logger.Error("统一网关订单服务调用失败",
			zap.String("customer_order_no", orderReq.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.String("provider", provider),
			zap.Error(err))

		// 🔥 修复：直接返回原始错误，让上层的categorizeError处理
		return nil, err
	}

	// 🔥 检查订单服务返回的Success状态
	if !orderResp.Success {
		// 🚀 记录订单服务调用失败
		h.logger.Error("统一网关订单服务调用失败",
			zap.String("customer_order_no", orderReq.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.String("provider", provider),
			zap.Bool("success", orderResp.Success),
			zap.String("error_message", orderResp.Message))

		// 🔥 返回错误，让上层的categorizeError处理，避免重复包装
		return nil, fmt.Errorf("%s", orderResp.Message)
	}

	// 🚀 新增：记录订单服务调用成功
	h.logger.Info("统一网关订单服务调用成功",
		zap.String("customer_order_no", orderReq.CustomerOrderNo),
		zap.String("user_id", req.UserID),
		zap.String("provider", provider),
		zap.Bool("success", orderResp.Success))

	// 🔄 转换为简化响应格式
	return h.convertToSimpleOrderResponse(orderResp, expressCode), nil
}

// parseOrderCodeWithValidation 解析下单代码并返回增强代码信息（用于一致性验证）
func (h *UnifiedGatewayHandler) parseOrderCodeWithValidation(orderCode string) (expressCode, originalCode, provider, channelID, productCode string, enhancedCode *EnhancedOrderCode, err error) {
	// 检查是否是增强版代码
	if strings.HasPrefix(orderCode, "ENHANCED_ORDER_CODE_") {
		code, err := h.parseEnhancedOrderCodeWithInfo(orderCode)
		if err != nil {
			return "", "", "", "", "", nil, err
		}
		return code.StandardCode, code.OriginalCode, code.Provider, code.ChannelID, code.ProductCode, code, nil
	}

	// 🚀 新增：支持京东快递专用下单代码
	if strings.HasPrefix(orderCode, "JD_ORDER_CODE_") {
		expressCode, originalCode, provider, channelID, productCode, err := h.parseSpecialOrderCode(orderCode, "JD_ORDER_CODE_")
		return expressCode, originalCode, provider, channelID, productCode, nil, err
	}

	// 🚀 新增：支持德邦快递专用下单代码
	if strings.HasPrefix(orderCode, "DBL_ORDER_CODE_") {
		expressCode, originalCode, provider, channelID, productCode, err := h.parseSpecialOrderCode(orderCode, "DBL_ORDER_CODE_")
		return expressCode, originalCode, provider, channelID, productCode, nil, err
	}

	// 🚀 新增：支持菜鸟简单格式订单代码（如：CAINIAO_**********_YUNDA_1752581958）
	if strings.HasPrefix(orderCode, "CAINIAO_") {
		expressCode, originalCode, provider, channelID, productCode, err := h.parseCainiaoSimpleOrderCode(orderCode)
		return expressCode, originalCode, provider, channelID, productCode, nil, err
	}

	// 兼容旧版本代码
	if strings.HasPrefix(orderCode, "ORDER_CODE_") {
		expressCode, originalCode, provider, channelID, productCode, err := h.parseOldOrderCode(orderCode)
		return expressCode, originalCode, provider, channelID, productCode, nil, err
	}

	return "", "", "", "", "", nil, fmt.Errorf("无效的下单代码格式")
}

// parseEnhancedOrderCodeWithInfo 解析增强版下单代码并返回完整信息
func (h *UnifiedGatewayHandler) parseEnhancedOrderCodeWithInfo(orderCode string) (*EnhancedOrderCode, error) {
	var encodedCode string
	var isEncrypted bool

	// 检查是否是加密版本
	if strings.HasPrefix(orderCode, "ENHANCED_ORDER_CODE_ENCRYPTED_") {
		encodedCode = strings.TrimPrefix(orderCode, "ENHANCED_ORDER_CODE_ENCRYPTED_")
		isEncrypted = true
	} else {
		encodedCode = strings.TrimPrefix(orderCode, "ENHANCED_ORDER_CODE_")
		isEncrypted = false
	}

	// Base64解码
	decodedBytes, err := base64.StdEncoding.DecodeString(encodedCode)
	if err != nil {
		return nil, fmt.Errorf("解码失败: %v", err)
	}

	var jsonData []byte
	if isEncrypted {
		// 解密数据
		jsonData, err = h.decryptOrderCodeData(decodedBytes)
		if err != nil {
			return nil, fmt.Errorf("解密失败: %v", err)
		}
	} else {
		jsonData = decodedBytes
	}

	// JSON反序列化
	var enhancedCode EnhancedOrderCode
	if err := json.Unmarshal(jsonData, &enhancedCode); err != nil {
		return nil, fmt.Errorf("反序列化失败: %v", err)
	}

	return &enhancedCode, nil
}

// parseOldOrderCode 解析旧版本下单代码（向后兼容）
func (h *UnifiedGatewayHandler) parseOldOrderCode(orderCode string) (expressCode, originalCode, provider, channelID, productCode string, err error) {
	// 去除前缀
	encodedCode := strings.TrimPrefix(orderCode, "ORDER_CODE_")

	// Base64解码
	decodedBytes, err := base64.StdEncoding.DecodeString(encodedCode)
	if err != nil {
		return "", "", "", "", "", fmt.Errorf("解码失败: %v", err)
	}

	// 解析字段
	// 格式: 标准代码|原始代码|供应商|渠道ID|产品代码
	parts := strings.Split(string(decodedBytes), "|")
	if len(parts) != 5 {
		return "", "", "", "", "", fmt.Errorf("下单代码格式错误")
	}

	return parts[0], parts[1], parts[2], parts[3], parts[4], nil
}

// parseSpecialOrderCode 解析特殊前缀的下单代码（京东快递、德邦快递等）
func (h *UnifiedGatewayHandler) parseSpecialOrderCode(orderCode, prefix string) (expressCode, originalCode, provider, channelID, productCode string, err error) {
	// 去除前缀
	encodedCode := strings.TrimPrefix(orderCode, prefix)

	// Base64解码
	decodedBytes, err := base64.StdEncoding.DecodeString(encodedCode)
	if err != nil {
		return "", "", "", "", "", fmt.Errorf("解码失败: %v", err)
	}

	// 解析字段
	// 格式: 快递代码|原始代码|供应商|渠道ID|产品代码
	parts := strings.Split(string(decodedBytes), "|")
	if len(parts) != 5 {
		return "", "", "", "", "", fmt.Errorf("下单代码格式错误")
	}

	return parts[0], parts[1], parts[2], parts[3], parts[4], nil
}

// decryptOrderCodeData 解密订单代码数据
func (h *UnifiedGatewayHandler) decryptOrderCodeData(encryptedData []byte) ([]byte, error) {
	// 使用固定的密钥（在实际生产环境中应该从配置中获取）
	key := []byte("go-kuaidi-order-code-encryption-key")
	if len(key) > 32 {
		key = key[:32]
	} else if len(key) < 32 {
		// 填充到32字节
		for len(key) < 32 {
			key = append(key, 0)
		}
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	if len(encryptedData) < aes.BlockSize {
		return nil, fmt.Errorf("密文太短")
	}

	iv := encryptedData[:aes.BlockSize]
	ciphertext := encryptedData[aes.BlockSize:]

	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(ciphertext, ciphertext)

	return ciphertext, nil
}

// convertToInternalOrderRequest 转换为内部订单请求格式
func (h *UnifiedGatewayHandler) convertToInternalOrderRequest(ctx context.Context, req SimpleOrderRequest, expressCode, originalCode, provider, channelID, productCode, userID string) (*model.OrderRequest, error) {
	// 设置默认值
	quantity := req.Quantity
	if quantity <= 0 {
		quantity = 1
	}

	goodsName := req.GoodsName
	if goodsName == "" {
		goodsName = "物品"
	}

	// 🔥 新增：优先使用用户传入的客户订单号，否则自动生成
	var customerOrderNo string
	if req.CustomerOrderNo != "" {
		// 使用用户传入的客户订单号
		customerOrderNo = req.CustomerOrderNo
	} else {
		// 生成供应商前缀的客户订单号
		customerOrderNo = fmt.Sprintf("gk%d", util.NowBeijing().Unix())
	}

	// 🔥 企业级修复：创建包裹信息，包含长宽高，优先使用长宽高计算体积
	volume := req.Volume
	if req.Length > 0 && req.Width > 0 && req.Height > 0 {
		// 长宽高单位是cm，需要转换为m³
		volumeCm3 := req.Length * req.Width * req.Height
		volume = volumeCm3 / 1000000 // cm³ → m³
	}

	// 🔥 关键修复：计算体积重量，确保下单时使用正确的计费重量
	chargedWeight := req.Weight
	if req.Length > 0 && req.Width > 0 && req.Height > 0 {
		// 计算体积重量（cm³ / 8000）- 四通一达标准
		volumeWeight := float64(req.Length*req.Width*req.Height) / 8000.0

		// 取体积重量和实际重量的较大值
		if volumeWeight > chargedWeight {
			chargedWeight = volumeWeight
		}
	}

	h.logger.Info("统一网关体积重量计算",
		zap.String("customer_order_no", customerOrderNo),
		zap.Float64("original_weight", req.Weight),
		zap.Float64("volume_weight", float64(req.Length*req.Width*req.Height)/8000.0),
		zap.Float64("charged_weight", chargedWeight),
		zap.Float64("length", req.Length),
		zap.Float64("width", req.Width),
		zap.Float64("height", req.Height))

	packageInfo := model.PackageInfo{
		Weight:    chargedWeight, // 🔥 关键修复：使用计费重量而不是原始重量
		Length:    req.Length,
		Width:     req.Width,
		Height:    req.Height,
		Volume:    volume,
		Quantity:  quantity,
		GoodsName: goodsName,
		Remark:    req.Remark, // 🔥 修复：添加备注字段
	}

	// 创建订单请求
	orderReq := &model.OrderRequest{
		CustomerOrderNo: customerOrderNo,
		UserID:          userID,       // 设置用户ID
		ExpressType:     originalCode, // 使用原始快递公司代码
		ProductType:     productCode,
		ChannelID:       channelID,
		Provider:        provider, // 设置从订单代码中解析出的供应商信息

		// 🔥 核心修复：自动设置价格验证字段（内部逻辑，用户无感知）
		PriceSource:   "cache",           // 自动标记为缓存价格，触发内部价格验证
		CachedPrice:   req.ExpectedPrice, // 🔥 使用用户传入的期望价格
		ExpectedPrice: req.ExpectedPrice, // 🔥 使用用户传入的期望价格

		Sender: model.SenderInfo{
			Name:     req.SenderName,
			Mobile:   req.SenderMobile,
			Province: req.SenderProvince,
			City:     req.SenderCity,
			District: req.SenderDistrict,
			Address:  req.SenderAddress,
		},
		Receiver: model.ReceiverInfo{
			Name:     req.ReceiverName,
			Mobile:   req.ReceiverMobile,
			Province: req.ReceiverProvince,
			City:     req.ReceiverCity,
			District: req.ReceiverDistrict,
			Address:  req.ReceiverAddress,
		},
		Package:   packageInfo,
		PayMethod: req.PayMethod,

		// 🔥 修复：设置预约时间信息
		Pickup: model.PickupInfo{
			StartTime: req.PickupStartTime,
			EndTime:   req.PickupEndTime,
		},
	}

	// 🔥 调试：记录预约时间信息传递
	h.logger.Info("统一网关：预约时间信息传递",
		zap.String("pickup_start_time", req.PickupStartTime),
		zap.String("pickup_end_time", req.PickupEndTime),
		zap.String("order_pickup_start_time", orderReq.Pickup.StartTime),
		zap.String("order_pickup_end_time", orderReq.Pickup.EndTime),
		zap.Bool("start_time_empty", req.PickupStartTime == ""),
		zap.Bool("end_time_empty", req.PickupEndTime == ""))

	return orderReq, nil
}

// convertToSimpleOrderResponse 转换为简化响应格式
func (h *UnifiedGatewayHandler) convertToSimpleOrderResponse(resp *model.OrderResponse, expressCode string) *SimpleOrderResponse {
	if !resp.Success {
		return &SimpleOrderResponse{
			Success: resp.Success,
			Code:    resp.Code,
			Message: resp.Message,
		}
	}

	// 标准化快递公司名称
	standardNameMap := map[string]string{
		"ZTO": "中通快递",
		"STO": "申通快递",
		"YTO": "圆通速递",
		"YD":  "韵达速递",
		"JD":  "京东物流",
		"JT":  "极兔快递",
		"SF":  "顺丰速运",
		"EMS": "EMS快递",
	}

	// 获取标准化的快递公司名称
	expressName := ""
	if name, ok := standardNameMap[expressCode]; ok {
		expressName = name
	} else {
		expressName = expressCode // 如果没有标准化名称，使用代码作为名称
	}

	return &SimpleOrderResponse{
		Success:         resp.Success,
		Code:            resp.Code,
		Message:         resp.Message,
		PlatformOrderNo: resp.Data.PlatformOrderNo,                                                         // 🔥 平台订单号
		CustomerOrderNo: resp.Data.CustomerOrderNo,                                                         // 🔥 客户订单号
		OrderNo:         getPrimaryOrderNoForUnified(resp.Data.PlatformOrderNo, resp.Data.CustomerOrderNo), // 🔥 主要展示字段：平台订单号
		WaybillNo:       resp.Data.TrackingNo,                                                              // 使用TrackingNo作为WaybillNo
		ExpressCode:     expressCode,
		ExpressName:     expressName,
		Price:           fmt.Sprintf("%.2f", resp.Data.Price),
		// 注意：不再返回供应商内部订单号
	}
}

// getPrimaryOrderNoForUnified 获取主要订单号（优先使用平台订单号）
func getPrimaryOrderNoForUnified(platformOrderNo, customerOrderNo string) string {
	if platformOrderNo != "" {
		return platformOrderNo
	}
	return customerOrderNo
}

// getUserIDFromContext 从认证上下文中获取用户ID（增强版）
func (h *UnifiedGatewayHandler) getUserIDFromContext(c *gin.Context, req *UnifiedRequest) (string, error) {
	// 🔥 优先尝试直接获取用户ID（JWT认证设置的）
	directUserIDKeys := []string{"userID", "user_id"}
	for _, key := range directUserIDKeys {
		if userID, exists := c.Get(key); exists {
			if uid, ok := userID.(string); ok && uid != "" {
				h.logger.Debug("成功获取用户ID（直接方式）",
					zap.String("user_id", uid),
					zap.String("source", key),
					zap.String("client_type", req.ClientType))
				return uid, nil
			}
		}
	}

	// 🔥 如果没有直接的用户ID，通过客户端ID查找用户ID
	clientIDKeys := []string{"client_id", "client_id_from_signature"}
	for _, key := range clientIDKeys {
		if clientID, exists := c.Get(key); exists {
			if cid, ok := clientID.(string); ok && cid != "" {
				h.logger.Debug("通过客户端ID查找用户ID",
					zap.String("client_id", cid),
					zap.String("source", key),
					zap.String("client_type", req.ClientType))

				// 通过用户身份解析器获取真实用户ID
				if resolver, exists := c.Get("user_identity_resolver"); exists {
					if identityResolver, ok := resolver.(*service.UserIdentityResolver); ok {
						userID, err := identityResolver.ResolveUserID(c)
						if err == nil && userID != "" {
							h.logger.Info("✅ 成功通过身份解析器获取用户ID",
								zap.String("client_id", cid),
								zap.String("user_id", userID),
								zap.String("client_type", req.ClientType))
							return userID, nil
						}
						h.logger.Warn("身份解析器解析失败",
							zap.String("client_id", cid),
							zap.Error(err))
					}
				}

				// 降级方案：直接查询用户仓库
				if h.userRepo != nil {
					user, err := h.userRepo.FindByClientID(cid)
					if err == nil && user != nil && user.IsActive {
						h.logger.Info("✅ 成功通过用户仓库获取用户ID",
							zap.String("client_id", cid),
							zap.String("user_id", user.ID),
							zap.String("username", user.Username),
							zap.String("client_type", req.ClientType))
						return user.ID, nil
					}
					h.logger.Warn("通过用户仓库查找用户失败",
						zap.String("client_id", cid),
						zap.Error(err))
				}
			}
		}
	}

	// 根据客户端类型获取用户ID（保留原有逻辑作为备用）
	switch strings.ToLower(req.ClientType) {
	case "web", "admin":
		// Web客户端：从JWT Token中获取用户ID
		if userID, exists := c.Get("user_id"); exists {
			if uid, ok := userID.(string); ok && uid != "" {
				h.logger.Debug("Web客户端用户ID获取成功",
					zap.String("user_id", uid),
					zap.String("client_type", req.ClientType))
				return uid, nil
			}
		}

		// 备用方案：从userID字段获取
		if userID, exists := c.Get("userID"); exists {
			if uid, ok := userID.(string); ok && uid != "" {
				h.logger.Debug("Web客户端用户ID获取成功（备用字段）",
					zap.String("user_id", uid),
					zap.String("client_type", req.ClientType))
				return uid, nil
			}
		}

		// 调试：记录上下文中的关键信息
		contextInfo := map[string]interface{}{}
		if claims, exists := c.Get("user_claims"); exists {
			contextInfo["user_claims"] = claims
		}
		if clientID, exists := c.Get("client_id"); exists {
			contextInfo["client_id"] = clientID
		}
		if clientType, exists := c.Get("client_type"); exists {
			contextInfo["client_type"] = clientType
		}

		h.logger.Error("Web客户端用户ID获取失败，上下文调试信息",
			zap.String("client_type", req.ClientType),
			zap.Any("context_info", contextInfo))

		return "", fmt.Errorf("user ID not found in JWT token")

	case "api", "":
		// API客户端：优先将client_id映射为真实user_id
		if clientID, exists := c.Get("client_id"); exists {
			if cid, ok := clientID.(string); ok && cid != "" {
				// 如果注入了userRepo，则尝试映射
				if h.userRepo != nil {
					if u, err := h.userRepo.FindByClientID(cid); err == nil && u != nil {
						return u.ID, nil
					}
				}
				// 回退：直接返回client_id
				return cid, nil
			}
		}
		// 备选方案：从请求中获取
		if req.Username != "" {
			if h.userRepo != nil {
				if u, err := h.userRepo.FindByClientID(req.Username); err == nil && u != nil {
					return u.ID, nil
				}
			}
			return req.Username, nil
		}
		return "", fmt.Errorf("client ID not found in request")

	default:
		return "", fmt.Errorf("unsupported client type: %s", req.ClientType)
	}
}

// cancelOrderInternal 内部取消订单逻辑
func (h *UnifiedGatewayHandler) cancelOrderInternal(c *gin.Context, params *CancelOrderBusinessParams, userID string) (*CancelOrderResponseData, error) {
	// 🚀 性能监控
	startTime := util.NowBeijing()
	defer func() {
		duration := time.Since(startTime)
		h.logger.Info("取消订单性能统计",
			zap.String("order_no", params.OrderNo),
			zap.String("platform_order_no", params.PlatformOrderNo),
			zap.String("user_id", userID),
			zap.Duration("duration", duration))
	}()

	// 🚀 复用现有的OrderCancellationService逻辑
	if h.orderService == nil {
		return nil, fmt.Errorf("order service not initialized")
	}

	// 转换为内部取消请求格式
	cancelReq := &model.CancelOrderRequest{
		OrderNo:         params.OrderNo,
		TrackingNo:      params.TrackingNo,
		PlatformOrderNo: params.PlatformOrderNo, // 🔥 新增：支持明确指定平台订单号
		Reason:          params.Reason,
		UserID:          userID, // 设置用户ID进行权限验证
	}

	// 调用订单服务取消订单
	h.logger.Debug("调用订单服务取消订单",
		zap.String("order_no", params.OrderNo),
		zap.String("platform_order_no", params.PlatformOrderNo),
		zap.String("user_id", userID))

	cancelResp, err := h.orderService.CancelOrder(c.Request.Context(), cancelReq)
	if err != nil {
		h.logger.Error("订单服务取消订单失败",
			zap.String("order_no", params.OrderNo),
			zap.String("platform_order_no", params.PlatformOrderNo),
			zap.String("user_id", userID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to cancel order: %w", err)
	}

	// 转换为统一响应格式
	return &CancelOrderResponseData{
		Success:   cancelResp.Success,
		Code:      cancelResp.Code,
		Message:   cancelResp.Message,
		OrderNo:   params.OrderNo,
		Status:    "cancelling", // 默认状态，实际状态需要从订单服务获取
		RequestID: fmt.Sprintf("req_%d_%s", util.NowBeijing().Unix(), generateRequestID()),
	}, nil
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	// 生成简单的请求ID
	return fmt.Sprintf("%x", util.NowBeijing().UnixNano()%1000000)
}

// handleQueryOrder 处理查询订单请求
func (h *UnifiedGatewayHandler) handleQueryOrder(c *gin.Context, req *UnifiedRequest) {
	// 🔄 解析业务参数
	var params QueryOrderBusinessParams
	if err := h.parseBusinessParams(req.BusinessParams, &params); err != nil {
		h.logger.Error("查询订单参数解析失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 🔐 验证必填参数
	if err := h.validateQueryOrderParams(&params); err != nil {
		h.logger.Warn("查询订单参数验证失败",
			zap.String("order_no", params.OrderNo),
			zap.String("tracking_no", params.TrackingNo),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Parameter validation failed", err.Error())
		return
	}

	// 🔐 获取用户ID（从认证上下文中获取）
	userID, err := h.getUserIDFromContext(c, req)
	if err != nil {
		h.logger.Error("获取用户身份失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusUnauthorized, "Authentication failed", err.Error())
		return
	}

	// 📊 记录查询订单请求
	h.logger.Info("处理查询订单请求",
		zap.String("order_no", params.OrderNo),
		zap.String("tracking_no", params.TrackingNo),
		zap.String("user_id", userID),
		zap.String("client_type", req.ClientType))

	// 🚀 调用现有的查询订单逻辑
	queryResp, err := h.queryOrderInternal(c, &params, userID)
	if err != nil {
		h.logger.Error("查询订单处理失败",
			zap.String("order_no", params.OrderNo),
			zap.String("tracking_no", params.TrackingNo),
			zap.String("user_id", userID),
			zap.Error(err))
		// 🔧 智能错误处理：区分不同类型的错误
		statusCode, message := h.categorizeError(err)
		h.sendError(c, statusCode, message, err.Error())
		return
	}

	// 📊 记录查询订单成功
	h.logger.Info("查询订单处理成功",
		zap.String("order_no", params.OrderNo),
		zap.String("tracking_no", params.TrackingNo),
		zap.String("user_id", userID),
		zap.Bool("success", queryResp.Success))

	// 📤 返回统一格式响应
	h.sendSuccess(c, queryResp)
}

// validateQueryOrderParams 验证查询订单参数
func (h *UnifiedGatewayHandler) validateQueryOrderParams(params *QueryOrderBusinessParams) error {
	// 🔐 企业级参数验证：至少需要一个查询条件
	hasQueryCondition := params.OrderNo != "" || params.TrackingNo != "" ||
		params.PlatformOrderNo != "" || params.CustomerOrderNo != "" || params.ProviderOrderNo != ""

	if !hasQueryCondition {
		return fmt.Errorf("至少需要提供一个查询条件：order_no、tracking_no、platform_order_no、customer_order_no或provider_order_no")
	}

	// 🔐 参数长度验证
	orderNoFields := map[string]string{
		"order_no":          params.OrderNo,
		"tracking_no":       params.TrackingNo,
		"platform_order_no": params.PlatformOrderNo,
		"customer_order_no": params.CustomerOrderNo,
		"provider_order_no": params.ProviderOrderNo,
	}

	for fieldName, fieldValue := range orderNoFields {
		if fieldValue != "" && len(fieldValue) > 100 {
			return fmt.Errorf("%s长度不能超过100个字符", fieldName)
		}
	}

	return nil
}

// queryOrderInternal 内部查询订单逻辑
func (h *UnifiedGatewayHandler) queryOrderInternal(c *gin.Context, params *QueryOrderBusinessParams, userID string) (*QueryOrderResponseData, error) {
	// 🚀 性能监控
	startTime := util.NowBeijing()
	defer func() {
		duration := time.Since(startTime)
		h.logger.Info("查询订单性能统计",
			zap.String("order_no", params.OrderNo),
			zap.String("tracking_no", params.TrackingNo),
			zap.String("user_id", userID),
			zap.Duration("duration", duration))
	}()

	// 🔥 使用生产级智能订单查找服务 - 直接使用OrderService的查询方法
	// smartFinder := service.NewSmartOrderFinder(h.orderService.GetOrderRepository(), h.logger)

	// 🔥 智能确定查询的订单号（按优先级选择）
	queryOrderNo := ""

	// 优先级：明确指定的字段 > 通用order_no字段 > tracking_no
	if params.PlatformOrderNo != "" {
		queryOrderNo = params.PlatformOrderNo
	} else if params.CustomerOrderNo != "" {
		queryOrderNo = params.CustomerOrderNo
	} else if params.ProviderOrderNo != "" {
		queryOrderNo = params.ProviderOrderNo
	} else if params.OrderNo != "" {
		queryOrderNo = params.OrderNo
	} else if params.TrackingNo != "" {
		queryOrderNo = params.TrackingNo
	}

	if queryOrderNo == "" {
		return nil, fmt.Errorf("订单号和运单号不能同时为空")
	}

	// 调用智能订单查找服务
	h.logger.Debug("调用智能订单查找服务",
		zap.String("query_order_no", queryOrderNo),
		zap.String("original_order_no", params.OrderNo),
		zap.String("original_tracking_no", params.TrackingNo),
		zap.String("user_id", userID))

	// 使用OrderService的查询方法
	queryReq := &model.QueryOrderRequest{
		OrderNo:    queryOrderNo,
		TrackingNo: params.TrackingNo,
		UserID:     userID,
	}

	queryResp, err := h.orderService.QueryOrder(c.Request.Context(), queryReq)
	if err != nil {
		h.logger.Error("订单查询失败",
			zap.String("query_order_no", queryOrderNo),
			zap.String("user_id", userID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to query order: %w", err)
	}

	// 如果查询失败，返回错误响应
	if !queryResp.Success {
		return &QueryOrderResponseData{
			Success: false,
			Code:    queryResp.Code,
			Message: queryResp.Message,
		}, nil
	}

	// 🔥 修复：类型转换并返回订单数据
	var orderDetail *EnhancedOrderDetail
	if orderData, ok := queryResp.Data.(*model.UserOrderDetail); ok {
		// 转换为EnhancedOrderDetail格式
		orderDetail = &EnhancedOrderDetail{
			PlatformOrderNo: orderData.OrderNo,         // 平台订单号
			CustomerOrderNo: orderData.CustomerOrderNo, // 客户订单号
			ProviderOrderNo: orderData.OrderNo,         // 供应商订单号（暂用OrderNo）
			TrackingNo:      orderData.TrackingNo,      // 运单号
			OrderNo:         orderData.OrderNo,         // 兼容字段
			ExpressType:     orderData.ExpressType,     // 快递类型
			Provider:        orderData.Provider,        // 供应商
			Status:          orderData.Status,          // 状态
			Price:           orderData.Price,           // 价格
			ActualFee:       orderData.ActualFee,       // 实收费用
			CreatedAt:       orderData.CreatedAt.Format(time.RFC3339),
			UpdatedAt:       orderData.UpdatedAt.Format(time.RFC3339),
		}
	}

	return &QueryOrderResponseData{
		Success: queryResp.Success,
		Code:    queryResp.Code,
		Message: queryResp.Message,
		Data:    orderDetail,
		Meta: &QueryMetadata{
			QueryType:       "smart_query",
			OriginalOrderNo: params.OrderNo,
			QueryTime:       time.Now().Format(time.RFC3339),
		},
	}, nil
}

// handleQueryTrack 处理物流轨迹查询请求
func (h *UnifiedGatewayHandler) handleQueryTrack(c *gin.Context, req *UnifiedRequest) {
	// 🔄 解析业务参数
	var params TrackQueryBusinessParams
	if err := h.parseBusinessParams(req.BusinessParams, &params); err != nil {
		h.logger.Error("物流轨迹查询参数解析失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 🔐 验证必填参数
	if err := h.validateTrackQueryParams(&params); err != nil {
		h.logger.Warn("物流轨迹查询参数验证失败",
			zap.String("tracking_no", params.TrackingNo),
			zap.String("order_no", params.OrderNo),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Parameter validation failed", err.Error())
		return
	}

	// 🔐 获取用户ID（支持Web和API客户端）
	var userID string
	if strings.ToLower(req.ClientType) == "web" || strings.ToLower(req.ClientType) == "admin" {
		// Web客户端：从JWT Token中获取用户ID
		if userIDValue, exists := c.Get("user_id"); exists {
			if uid, ok := userIDValue.(string); ok {
				userID = uid
			}
		}
		if userID == "" {
			h.logger.Warn("Web客户端用户ID获取失败",
				zap.String("client_type", req.ClientType))
			h.sendError(c, http.StatusUnauthorized, "User authentication failed", "Unable to get user ID from token")
			return
		}
	} else {
		// API客户端：使用客户端ID作为用户ID
		userID = req.Username
	}

	// 🚀 调用现有的物流轨迹查询逻辑
	trackResp, err := h.queryTrackInternal(c, &params, userID)
	if err != nil {
		h.logger.Error("物流轨迹查询处理失败",
			zap.String("tracking_no", params.TrackingNo),
			zap.String("order_no", params.OrderNo),
			zap.String("user_id", userID),
			zap.Error(err))
		// 🔧 智能错误处理：区分不同类型的错误
		statusCode, message := h.categorizeError(err)
		h.sendError(c, statusCode, message, err.Error())
		return
	}

	// 📊 记录物流轨迹查询成功
	h.logger.Info("物流轨迹查询处理成功",
		zap.String("tracking_no", params.TrackingNo),
		zap.String("order_no", params.OrderNo),
		zap.String("user_id", userID),
		zap.Bool("success", trackResp.Success))

	// 📤 返回统一格式响应
	h.sendSuccess(c, trackResp)
}

// validateTrackQueryParams 验证物流轨迹查询参数
func (h *UnifiedGatewayHandler) validateTrackQueryParams(params *TrackQueryBusinessParams) error {
	// 🔐 企业级参数验证：运单号是必填项
	if params.TrackingNo == "" {
		return fmt.Errorf("运单号不能为空")
	}

	// 🔐 验证运单号格式（基本长度检查）
	if len(params.TrackingNo) < 8 || len(params.TrackingNo) > 30 {
		return fmt.Errorf("运单号长度应在8-30位之间")
	}

	// 🔐 验证手机号格式（如果提供）
	if params.Phone != "" {
		// 🔥 修复：使用生产级电话验证器，支持多种格式
		validator := util.GetPhoneValidator()
		result := validator.ValidatePhone(params.Phone)

		if !result.IsValid {
			return fmt.Errorf("手机号格式不正确，请提供有效的手机号（如：***********）或固定电话（如：010-12345678 或 28698723）")
		}
	}

	return nil
}

// queryTrackInternal 内部物流轨迹查询逻辑
func (h *UnifiedGatewayHandler) queryTrackInternal(c *gin.Context, params *TrackQueryBusinessParams, userID string) (*TrackQueryResponseData, error) {
	// 🚀 性能监控
	startTime := util.NowBeijing()
	defer func() {
		duration := time.Since(startTime)
		h.logger.Info("物流轨迹查询性能统计",
			zap.String("tracking_no", params.TrackingNo),
			zap.String("order_no", params.OrderNo),
			zap.String("user_id", userID),
			zap.Duration("duration", duration))
	}()

	// 🚀 复用现有的OrderService逻辑
	if h.orderService == nil {
		return nil, fmt.Errorf("order service not initialized")
	}

	// 转换为内部查询请求格式
	trackReq := &model.TrackQueryRequest{
		OrderNo:    params.OrderNo,
		TrackingNo: params.TrackingNo,
		Phone:      params.Phone,
		From:       params.From,
		To:         params.To,
		PollToken:  params.PollToken,
	}

	// 调用订单服务查询物流轨迹
	h.logger.Debug("调用订单服务查询物流轨迹",
		zap.String("tracking_no", params.TrackingNo),
		zap.String("order_no", params.OrderNo),
		zap.String("user_id", userID))

	trackResp, err := h.orderService.QueryTrack(c.Request.Context(), trackReq)
	if err != nil {
		h.logger.Error("订单服务查询物流轨迹失败",
			zap.String("tracking_no", params.TrackingNo),
			zap.String("order_no", params.OrderNo),
			zap.String("user_id", userID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to query track: %w", err)
	}

	// 转换为统一响应格式
	return &TrackQueryResponseData{
		Success: trackResp.Success,
		Code:    trackResp.Code,
		Message: trackResp.Message,
		Data:    trackResp.Data,
	}, nil
}

// 🎫 工单相关处理方法

// handleCreateWorkOrder 处理创建工单请求
func (h *UnifiedGatewayHandler) handleCreateWorkOrder(c *gin.Context, req *UnifiedRequest) {
	// 🔄 解析业务参数
	var params CreateWorkOrderBusinessParams
	if err := h.parseBusinessParams(req.BusinessParams, &params); err != nil {
		h.logger.Error("创建工单参数解析失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 🔐 验证必填参数
	if err := h.validateCreateWorkOrderParams(&params); err != nil {
		h.logger.Warn("创建工单参数验证失败",
			zap.Int("work_order_type", params.WorkOrderType),
			zap.String("content", params.Content),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Parameter validation failed", err.Error())
		return
	}

	// 🔐 获取用户ID（从认证上下文中获取）
	userID, err := h.getUserIDFromContext(c, req)
	if err != nil {
		h.logger.Error("获取用户身份失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusUnauthorized, "Authentication failed", err.Error())
		return
	}

	// 📊 记录创建工单请求
	h.logger.Info("处理创建工单请求",
		zap.Int("work_order_type", params.WorkOrderType),
		zap.String("content", params.Content),
		zap.String("user_id", userID),
		zap.String("client_type", req.ClientType))

	// 🔥 新增：转换参数格式
	createReq := &model.SmartCreateWorkOrderRequest{
		WorkOrderType:       params.WorkOrderType,
		Content:             params.Content,
		CustomerWorkOrderID: params.CustomerWorkOrderID, // 🔥 新增：用户自定义工单ID
		OrderNo:             params.OrderNo,
		TrackingNo:          params.TrackingNo,
		FeedbackWeight:      params.FeedbackWeight,
		GoodsValue:          params.GoodsValue,
		OverweightAmount:    params.OverweightAmount,
		AttachmentURLs:      params.AttachmentURLs,
		CallbackURL:         params.CallbackURL,
		MessageCallbackURL:  params.MessageCallbackURL,
	}

	// 🚀 调用现有的创建工单逻辑
	createResp, err := h.createWorkOrderInternal(c, createReq, userID)
	if err != nil {
		h.logger.Error("创建工单处理失败",
			zap.Int("work_order_type", params.WorkOrderType),
			zap.String("user_id", userID),
			zap.Error(err))
		h.sendError(c, http.StatusInternalServerError, "Work order creation failed", err.Error())
		return
	}

	// 📊 记录创建工单成功
	h.logger.Info("创建工单处理成功",
		zap.String("work_order_id", createResp.WorkOrderID),
		zap.String("user_id", userID),
		zap.Bool("success", createResp.Success),
		zap.String("provider", createResp.Provider),
		zap.String("request_id", createResp.RequestID))

	// 📤 返回统一格式响应
	h.sendSuccess(c, createResp)
}

// handleQueryWorkOrder 处理查询工单请求
func (h *UnifiedGatewayHandler) handleQueryWorkOrder(c *gin.Context, req *UnifiedRequest) {
	// 🔄 解析业务参数
	var params QueryWorkOrderBusinessParams
	if err := h.parseBusinessParams(req.BusinessParams, &params); err != nil {
		h.logger.Error("查询工单参数解析失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 🔐 验证必填参数
	if err := h.validateQueryWorkOrderParams(&params); err != nil {
		h.logger.Warn("查询工单参数验证失败",
			zap.String("work_order_id", params.WorkOrderID),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Parameter validation failed", err.Error())
		return
	}

	// 🔐 获取用户ID（从认证上下文中获取）
	userID, err := h.getUserIDFromContext(c, req)
	if err != nil {
		h.logger.Error("获取用户身份失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusUnauthorized, "Authentication failed", err.Error())
		return
	}

	// 📊 记录查询工单请求
	h.logger.Info("处理查询工单请求",
		zap.String("work_order_id", params.WorkOrderID),
		zap.String("user_id", userID),
		zap.String("client_type", req.ClientType))

	// 🚀 调用现有的查询工单逻辑
	queryResp, err := h.queryWorkOrderInternal(c, &params, userID)
	if err != nil {
		h.logger.Error("查询工单处理失败",
			zap.String("work_order_id", params.WorkOrderID),
			zap.String("user_id", userID),
			zap.Error(err))
		h.sendError(c, http.StatusInternalServerError, "Work order query failed", err.Error())
		return
	}

	// 📊 记录查询工单成功
	h.logger.Info("查询工单处理成功",
		zap.String("work_order_id", params.WorkOrderID),
		zap.String("user_id", userID),
		zap.Bool("success", queryResp.Success))

	// 📤 返回统一格式响应
	h.sendSuccess(c, queryResp)
}

// handleListWorkOrders 处理工单列表请求
func (h *UnifiedGatewayHandler) handleListWorkOrders(c *gin.Context, req *UnifiedRequest) {
	// 🔄 解析业务参数
	var params ListWorkOrdersBusinessParams
	if err := h.parseBusinessParams(req.BusinessParams, &params); err != nil {
		h.logger.Error("工单列表参数解析失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 🔐 验证和设置默认参数
	h.setDefaultListWorkOrdersParams(&params)

	// 🔐 获取用户ID（从认证上下文中获取）
	userID, err := h.getUserIDFromContext(c, req)
	if err != nil {
		h.logger.Error("获取用户身份失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusUnauthorized, "Authentication failed", err.Error())
		return
	}

	// 📊 记录工单列表请求
	h.logger.Info("处理工单列表请求",
		zap.Int("page", params.Page),
		zap.Int("page_size", params.PageSize),
		zap.String("user_id", userID),
		zap.String("client_type", req.ClientType))

	// 🚀 调用现有的工单列表逻辑
	listResp, err := h.listWorkOrdersInternal(c, &params, userID)
	if err != nil {
		h.logger.Error("工单列表处理失败",
			zap.String("user_id", userID),
			zap.Error(err))
		h.sendError(c, http.StatusInternalServerError, "Work order list query failed", err.Error())
		return
	}

	// 📊 记录工单列表成功
	h.logger.Info("工单列表处理成功",
		zap.String("user_id", userID),
		zap.Bool("success", listResp.Success))

	// 📤 返回统一格式响应
	h.sendSuccess(c, listResp)
}

// handleReplyWorkOrder 处理回复工单请求
func (h *UnifiedGatewayHandler) handleReplyWorkOrder(c *gin.Context, req *UnifiedRequest) {
	// 🔄 解析业务参数
	var params ReplyWorkOrderBusinessParams
	if err := h.parseBusinessParams(req.BusinessParams, &params); err != nil {
		h.logger.Error("回复工单参数解析失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 🔐 验证必填参数
	if err := h.validateReplyWorkOrderParams(&params); err != nil {
		h.logger.Warn("回复工单参数验证失败",
			zap.String("work_order_id", params.WorkOrderID),
			zap.String("content", params.Content),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Parameter validation failed", err.Error())
		return
	}

	// 🔐 获取用户ID（从认证上下文中获取）
	userID, err := h.getUserIDFromContext(c, req)
	if err != nil {
		h.logger.Error("获取用户身份失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusUnauthorized, "Authentication failed", err.Error())
		return
	}

	// 📊 记录回复工单请求
	h.logger.Info("处理回复工单请求",
		zap.String("work_order_id", params.WorkOrderID),
		zap.String("content", params.Content),
		zap.String("user_id", userID),
		zap.String("client_type", req.ClientType))

	// 🚀 调用现有的回复工单逻辑
	replyResp, err := h.replyWorkOrderInternal(c, &params, userID)
	if err != nil {
		h.logger.Error("回复工单处理失败",
			zap.String("work_order_id", params.WorkOrderID),
			zap.String("user_id", userID),
			zap.Error(err))
		h.sendError(c, http.StatusInternalServerError, "Work order reply failed", err.Error())
		return
	}

	// 📊 记录回复工单成功
	h.logger.Info("回复工单处理成功",
		zap.String("work_order_id", params.WorkOrderID),
		zap.String("reply_id", replyResp.ReplyID),
		zap.String("user_id", userID),
		zap.Bool("success", replyResp.Success))

	// 📤 返回统一格式响应
	h.sendSuccess(c, replyResp)
}

// handleDeleteWorkOrder 处理删除工单请求
func (h *UnifiedGatewayHandler) handleDeleteWorkOrder(c *gin.Context, req *UnifiedRequest) {
	// 🔄 解析业务参数
	var params DeleteWorkOrderBusinessParams
	if err := h.parseBusinessParams(req.BusinessParams, &params); err != nil {
		h.logger.Error("删除工单参数解析失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 🔐 验证必填参数
	if err := h.validateDeleteWorkOrderParams(&params); err != nil {
		h.logger.Warn("删除工单参数验证失败",
			zap.String("work_order_id", params.WorkOrderID),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Parameter validation failed", err.Error())
		return
	}

	// 🔐 获取用户ID（从认证上下文中获取）
	userID, err := h.getUserIDFromContext(c, req)
	if err != nil {
		h.logger.Error("获取用户身份失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusUnauthorized, "Authentication failed", err.Error())
		return
	}

	// 📊 记录删除工单请求
	h.logger.Info("处理删除工单请求",
		zap.String("work_order_id", params.WorkOrderID),
		zap.String("user_id", userID),
		zap.String("client_type", req.ClientType))

	// 🚀 调用现有的删除工单逻辑
	deleteResp, err := h.deleteWorkOrderInternal(c, &params, userID)
	if err != nil {
		h.logger.Error("删除工单处理失败",
			zap.String("work_order_id", params.WorkOrderID),
			zap.String("user_id", userID),
			zap.Error(err))
		h.sendError(c, http.StatusInternalServerError, "Work order deletion failed", err.Error())
		return
	}

	// 📊 记录删除工单成功
	h.logger.Info("删除工单处理成功",
		zap.String("work_order_id", params.WorkOrderID),
		zap.String("user_id", userID),
		zap.Bool("success", deleteResp.Success))

	// 📤 返回统一格式响应
	h.sendSuccess(c, deleteResp)
}

// handleUploadWorkOrderAttachment 处理上传工单附件请求
func (h *UnifiedGatewayHandler) handleUploadWorkOrderAttachment(c *gin.Context, req *UnifiedRequest) {
	// 🔄 解析业务参数
	var params UploadWorkOrderAttachmentBusinessParams
	if err := h.parseBusinessParams(req.BusinessParams, &params); err != nil {
		h.logger.Error("上传工单附件参数解析失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 🔐 验证必填参数
	if err := h.validateUploadWorkOrderAttachmentParams(&params); err != nil {
		h.logger.Warn("上传工单附件参数验证失败",
			zap.String("file_name", params.FileName),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Parameter validation failed", err.Error())
		return
	}

	// 🔐 获取用户ID（从认证上下文中获取）
	userID, err := h.getUserIDFromContext(c, req)
	if err != nil {
		h.logger.Error("获取用户身份失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusUnauthorized, "Authentication failed", err.Error())
		return
	}

	// 📊 记录上传工单附件请求
	h.logger.Info("处理上传工单附件请求",
		zap.String("file_name", params.FileName),
		zap.String("user_id", userID),
		zap.String("client_type", req.ClientType))

	// 🚀 调用现有的上传附件逻辑
	uploadResp, err := h.uploadWorkOrderAttachmentInternal(c, &params, userID)
	if err != nil {
		h.logger.Error("上传工单附件处理失败",
			zap.String("file_name", params.FileName),
			zap.String("user_id", userID),
			zap.Error(err))
		h.sendError(c, http.StatusInternalServerError, "Work order attachment upload failed", err.Error())
		return
	}

	// 📊 记录上传工单附件成功
	h.logger.Info("上传工单附件处理成功",
		zap.String("file_name", params.FileName),
		zap.String("attachment_id", uploadResp.AttachmentID),
		zap.String("file_url", uploadResp.FileURL),
		zap.String("user_id", userID),
		zap.Bool("success", uploadResp.Success))

	// 📤 返回统一格式响应
	h.sendSuccess(c, uploadResp)
}

// 🎫 工单参数验证方法

// validateCreateWorkOrderParams 验证创建工单参数
func (h *UnifiedGatewayHandler) validateCreateWorkOrderParams(params *CreateWorkOrderBusinessParams) error {
	// 验证工单类型
	if params.WorkOrderType <= 0 {
		return fmt.Errorf("工单类型不能为空")
	}

	// 验证支持的工单类型（6种核心类型）
	supportedTypes := map[int]string{
		1:  "催取件",
		2:  "重量异常",
		12: "催派送",
		16: "物流停滞",
		17: "重新分配快递员",
		19: "取消订单",
	}
	if _, exists := supportedTypes[params.WorkOrderType]; !exists {
		return fmt.Errorf("不支持的工单类型: %d", params.WorkOrderType)
	}

	// 验证问题描述
	if strings.TrimSpace(params.Content) == "" {
		return fmt.Errorf("问题描述不能为空")
	}
	if len(params.Content) > 1000 {
		return fmt.Errorf("问题描述不能超过1000个字符")
	}

	// 🔥 新增：验证用户自定义工单ID格式
	if params.CustomerWorkOrderID != nil && *params.CustomerWorkOrderID != "" {
		if err := h.validateCustomerWorkOrderID(*params.CustomerWorkOrderID); err != nil {
			return fmt.Errorf("用户自定义工单ID格式错误: %w", err)
		}
	}

	// 验证订单标识（订单号或运单号至少提供一个）
	if (params.OrderNo == nil || *params.OrderNo == "") && (params.TrackingNo == nil || *params.TrackingNo == "") {
		return fmt.Errorf("订单号或运单号至少提供一个")
	}

	// 验证重量异常工单的特殊要求
	if params.WorkOrderType == 2 && params.FeedbackWeight == nil {
		return fmt.Errorf("重量异常工单必须提供反馈重量")
	}

	return nil
}

// validateQueryWorkOrderParams 验证查询工单参数
func (h *UnifiedGatewayHandler) validateQueryWorkOrderParams(params *QueryWorkOrderBusinessParams) error {
	if strings.TrimSpace(params.WorkOrderID) == "" {
		return fmt.Errorf("工单ID不能为空")
	}
	return nil
}

// 🔥 新增：验证用户自定义工单ID格式
func (h *UnifiedGatewayHandler) validateCustomerWorkOrderID(customerWorkOrderID string) error {
	// 长度验证：1-64字符
	if len(customerWorkOrderID) < 1 || len(customerWorkOrderID) > 64 {
		return fmt.Errorf("用户自定义工单ID长度必须在1-64个字符之间")
	}

	// 格式验证：仅允许字母、数字、下划线、连字符
	validPattern := regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)
	if !validPattern.MatchString(customerWorkOrderID) {
		return fmt.Errorf("用户自定义工单ID只能包含字母、数字、下划线和连字符")
	}

	return nil
}

// setDefaultListWorkOrdersParams 设置工单列表默认参数
func (h *UnifiedGatewayHandler) setDefaultListWorkOrdersParams(params *ListWorkOrdersBusinessParams) {
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageSize > 100 {
		params.PageSize = 100 // 限制最大页面大小
	}
}

// validateReplyWorkOrderParams 验证回复工单参数
func (h *UnifiedGatewayHandler) validateReplyWorkOrderParams(params *ReplyWorkOrderBusinessParams) error {
	if strings.TrimSpace(params.WorkOrderID) == "" {
		return fmt.Errorf("工单ID不能为空")
	}
	if strings.TrimSpace(params.Content) == "" {
		return fmt.Errorf("回复内容不能为空")
	}
	if len(params.Content) > 1000 {
		return fmt.Errorf("回复内容不能超过1000个字符")
	}
	return nil
}

// validateDeleteWorkOrderParams 验证删除工单参数
func (h *UnifiedGatewayHandler) validateDeleteWorkOrderParams(params *DeleteWorkOrderBusinessParams) error {
	if strings.TrimSpace(params.WorkOrderID) == "" {
		return fmt.Errorf("工单ID不能为空")
	}
	return nil
}

// validateUploadWorkOrderAttachmentParams 验证上传工单附件参数
func (h *UnifiedGatewayHandler) validateUploadWorkOrderAttachmentParams(params *UploadWorkOrderAttachmentBusinessParams) error {
	if strings.TrimSpace(params.FileName) == "" {
		return fmt.Errorf("文件名不能为空")
	}
	if strings.TrimSpace(params.FileContent) == "" {
		return fmt.Errorf("文件内容不能为空")
	}

	// 验证文件名格式
	if len(params.FileName) > 255 {
		return fmt.Errorf("文件名不能超过255个字符")
	}

	// 验证Base64编码的文件内容
	if _, err := base64.StdEncoding.DecodeString(params.FileContent); err != nil {
		return fmt.Errorf("文件内容必须是有效的Base64编码")
	}

	return nil
}

// 🎫 工单内部业务逻辑方法

// createWorkOrderInternal 内部创建工单逻辑
func (h *UnifiedGatewayHandler) createWorkOrderInternal(c *gin.Context, req *model.SmartCreateWorkOrderRequest, userID string) (*CreateWorkOrderResponseData, error) {
	// 🚀 性能监控
	startTime := util.NowBeijing()
	defer func() {
		duration := time.Since(startTime)
		h.logger.Info("创建工单性能统计",
			zap.Int("work_order_type", req.WorkOrderType),
			zap.String("user_id", userID),
			zap.Duration("duration", duration))
	}()

	// 🚀 复用现有的WorkOrderService逻辑
	if h.workOrderService == nil {
		return nil, fmt.Errorf("work order service not initialized")
	}

	// 调用工单服务创建工单
	h.logger.Debug("调用工单服务创建工单",
		zap.Int("work_order_type", req.WorkOrderType),
		zap.String("user_id", userID))

	workOrder, err := h.workOrderService.CreateWorkOrder(c.Request.Context(), userID, req)
	if err != nil {
		h.logger.Error("工单服务创建工单失败",
			zap.Int("work_order_type", req.WorkOrderType),
			zap.String("user_id", userID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to create work order: %w", err)
	}

	// 转换为统一响应格式
	return &CreateWorkOrderResponseData{
		Success:             true,
		Code:                200,
		Message:             "工单创建成功",
		WorkOrderID:         workOrder.ID.String(),
		CustomerWorkOrderID: *workOrder.CustomerWorkOrderID, // 🔥 新增：返回用户自定义工单ID
		WorkOrderType:       workOrder.WorkOrderType,
		Title:               workOrder.Title,
		Status:              int(workOrder.Status),
		Provider:            workOrder.Provider,
		RequestID:           fmt.Sprintf("req_%d_%s", util.NowBeijing().Unix(), generateRequestID()),
	}, nil
}

// queryWorkOrderInternal 内部查询工单逻辑
func (h *UnifiedGatewayHandler) queryWorkOrderInternal(c *gin.Context, params *QueryWorkOrderBusinessParams, userID string) (*QueryWorkOrderResponseData, error) {
	// 🚀 性能监控
	startTime := util.NowBeijing()
	defer func() {
		duration := time.Since(startTime)
		h.logger.Info("查询工单性能统计",
			zap.String("work_order_id", params.WorkOrderID),
			zap.String("user_id", userID),
			zap.Duration("duration", duration))
	}()

	// 🚀 复用现有的WorkOrderService逻辑
	if h.workOrderService == nil {
		return nil, fmt.Errorf("work order service not initialized")
	}

	// 解析工单ID
	workOrderUUID, err := uuid.Parse(params.WorkOrderID)
	if err != nil {
		return nil, fmt.Errorf("invalid work order ID format: %w", err)
	}

	// 调用工单服务查询工单
	h.logger.Debug("调用工单服务查询工单",
		zap.String("work_order_id", params.WorkOrderID),
		zap.String("user_id", userID))

	workOrder, err := h.workOrderService.GetWorkOrder(c.Request.Context(), userID, workOrderUUID)
	if err != nil {
		h.logger.Error("工单服务查询工单失败",
			zap.String("work_order_id", params.WorkOrderID),
			zap.String("user_id", userID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to query work order: %w", err)
	}

	// 转换为统一响应格式
	return &QueryWorkOrderResponseData{
		Success: true,
		Code:    200,
		Message: "查询工单成功",
		Data:    workOrder,
	}, nil
}

// listWorkOrdersInternal 内部工单列表逻辑
func (h *UnifiedGatewayHandler) listWorkOrdersInternal(c *gin.Context, params *ListWorkOrdersBusinessParams, userID string) (*ListWorkOrdersResponseData, error) {
	// 🚀 性能监控
	startTime := util.NowBeijing()
	defer func() {
		duration := time.Since(startTime)
		h.logger.Info("工单列表性能统计",
			zap.Int("page", params.Page),
			zap.Int("page_size", params.PageSize),
			zap.String("user_id", userID),
			zap.Duration("duration", duration))
	}()

	// 🚀 复用现有的WorkOrderService逻辑
	if h.workOrderService == nil {
		return nil, fmt.Errorf("work order service not initialized")
	}

	// 转换为内部列表请求格式
	listReq := &model.WorkOrderListRequest{
		Page:          params.Page,
		PageSize:      params.PageSize,
		Provider:      params.Provider,
		Status:        params.Status,
		WorkOrderType: params.WorkOrderType,
		OrderNo:       params.OrderNo,
		TrackingNo:    params.TrackingNo,
		StartDate:     params.StartDate,
		EndDate:       params.EndDate,
	}

	// 调用工单服务获取列表
	h.logger.Debug("调用工单服务获取列表",
		zap.Int("page", params.Page),
		zap.Int("page_size", params.PageSize),
		zap.String("user_id", userID))

	listResp, err := h.workOrderService.ListWorkOrders(c.Request.Context(), userID, listReq)
	if err != nil {
		h.logger.Error("工单服务获取列表失败",
			zap.String("user_id", userID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to list work orders: %w", err)
	}

	// 转换为统一响应格式
	return &ListWorkOrdersResponseData{
		Success: true,
		Code:    200,
		Message: "获取工单列表成功",
		Data:    listResp,
	}, nil
}

// replyWorkOrderInternal 内部回复工单逻辑
func (h *UnifiedGatewayHandler) replyWorkOrderInternal(c *gin.Context, params *ReplyWorkOrderBusinessParams, userID string) (*ReplyWorkOrderResponseData, error) {
	// 🚀 性能监控
	startTime := util.NowBeijing()
	defer func() {
		duration := time.Since(startTime)
		h.logger.Info("回复工单性能统计",
			zap.String("work_order_id", params.WorkOrderID),
			zap.String("user_id", userID),
			zap.Duration("duration", duration))
	}()

	// 🚀 复用现有的WorkOrderService逻辑
	if h.workOrderService == nil {
		return nil, fmt.Errorf("work order service not initialized")
	}

	// 解析工单ID
	workOrderUUID, err := uuid.Parse(params.WorkOrderID)
	if err != nil {
		return nil, fmt.Errorf("invalid work order ID format: %w", err)
	}

	// 转换为内部回复请求格式
	replyReq := &model.ReplyWorkOrderRequest{
		Content:        params.Content,
		AttachmentURLs: params.AttachmentURLs,
	}

	// 调用工单服务回复工单
	h.logger.Debug("调用工单服务回复工单",
		zap.String("work_order_id", params.WorkOrderID),
		zap.String("content", params.Content),
		zap.String("user_id", userID))

	reply, err := h.workOrderService.ReplyWorkOrder(c.Request.Context(), userID, workOrderUUID, replyReq)
	if err != nil {
		h.logger.Error("工单服务回复工单失败",
			zap.String("work_order_id", params.WorkOrderID),
			zap.String("user_id", userID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to reply work order: %w", err)
	}

	// 转换为统一响应格式
	return &ReplyWorkOrderResponseData{
		Success:   true,
		Code:      200,
		Message:   "回复工单成功",
		ReplyID:   reply.ID.String(),
		RequestID: fmt.Sprintf("req_%d_%s", util.NowBeijing().Unix(), generateRequestID()),
	}, nil
}

// deleteWorkOrderInternal 内部删除工单逻辑
func (h *UnifiedGatewayHandler) deleteWorkOrderInternal(c *gin.Context, params *DeleteWorkOrderBusinessParams, userID string) (*DeleteWorkOrderResponseData, error) {
	// 🚀 性能监控
	startTime := util.NowBeijing()
	defer func() {
		duration := time.Since(startTime)
		h.logger.Info("删除工单性能统计",
			zap.String("work_order_id", params.WorkOrderID),
			zap.String("user_id", userID),
			zap.Duration("duration", duration))
	}()

	// 🚀 复用现有的WorkOrderService逻辑
	if h.workOrderService == nil {
		return nil, fmt.Errorf("work order service not initialized")
	}

	// 解析工单ID
	workOrderUUID, err := uuid.Parse(params.WorkOrderID)
	if err != nil {
		return nil, fmt.Errorf("invalid work order ID format: %w", err)
	}

	// 调用工单服务删除工单
	h.logger.Debug("调用工单服务删除工单",
		zap.String("work_order_id", params.WorkOrderID),
		zap.String("user_id", userID))

	err = h.workOrderService.DeleteWorkOrder(c.Request.Context(), userID, workOrderUUID)
	if err != nil {
		h.logger.Error("工单服务删除工单失败",
			zap.String("work_order_id", params.WorkOrderID),
			zap.String("user_id", userID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to delete work order: %w", err)
	}

	// 转换为统一响应格式
	return &DeleteWorkOrderResponseData{
		Success:   true,
		Code:      200,
		Message:   "删除工单成功",
		RequestID: fmt.Sprintf("req_%d_%s", util.NowBeijing().Unix(), generateRequestID()),
	}, nil
}

// uploadWorkOrderAttachmentInternal 内部上传工单附件逻辑
func (h *UnifiedGatewayHandler) uploadWorkOrderAttachmentInternal(c *gin.Context, params *UploadWorkOrderAttachmentBusinessParams, userID string) (*UploadWorkOrderAttachmentResponseData, error) {
	// 🚀 性能监控
	startTime := util.NowBeijing()
	defer func() {
		duration := time.Since(startTime)
		h.logger.Info("上传工单附件性能统计",
			zap.String("file_name", params.FileName),
			zap.String("user_id", userID),
			zap.Duration("duration", duration))
	}()

	// 🚀 复用现有的WorkOrderService逻辑
	if h.workOrderService == nil {
		return nil, fmt.Errorf("work order service not initialized")
	}

	// 解码Base64文件内容
	fileContent, err := base64.StdEncoding.DecodeString(params.FileContent)
	if err != nil {
		return nil, fmt.Errorf("failed to decode file content: %w", err)
	}

	// 调用工单服务上传附件
	h.logger.Debug("调用工单服务上传附件",
		zap.String("file_name", params.FileName),
		zap.Int("file_size", len(fileContent)),
		zap.String("user_id", userID))

	uploadResp, err := h.workOrderService.UploadAttachment(c.Request.Context(), userID, params.FileName, fileContent)
	if err != nil {
		h.logger.Error("工单服务上传附件失败",
			zap.String("file_name", params.FileName),
			zap.String("user_id", userID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to upload work order attachment: %w", err)
	}

	// 转换为统一响应格式
	return &UploadWorkOrderAttachmentResponseData{
		Success:      true,
		Code:         200,
		Message:      "上传附件成功",
		AttachmentID: uploadResp.FileName, // 🔧 修复：使用FileName作为AttachmentID
		FileURL:      uploadResp.FileURL,
		RequestID:    fmt.Sprintf("req_%d_%s", util.NowBeijing().Unix(), generateRequestID()),
	}, nil
}

// handleQueryRealtimePrice 处理实时价格查询
func (h *UnifiedGatewayHandler) handleQueryRealtimePrice(c *gin.Context, req *UnifiedRequest) {
	// 解析业务参数
	var realtimePriceReq RealtimePriceRequest
	if err := h.parseBusinessParams(req.BusinessParams, &realtimePriceReq); err != nil {
		h.logger.Error("实时查价参数解析失败",
			zap.String("client_type", req.ClientType),
			zap.String("username", req.Username),
			zap.Error(err))
		h.sendError(c, http.StatusBadRequest, "Invalid business parameters", err.Error())
		return
	}

	// 记录查询开始
	h.logger.Info("开始处理实时价格查询",
		zap.String("client_type", req.ClientType),
		zap.String("username", req.Username),
		zap.String("sender_province", realtimePriceReq.Sender.Province),
		zap.String("sender_city", realtimePriceReq.Sender.City),
		zap.String("receiver_province", realtimePriceReq.Receiver.Province),
		zap.String("receiver_city", realtimePriceReq.Receiver.City),
		zap.Float64("weight", realtimePriceReq.Weight))

	// 调用实时查价处理器
	resp, err := h.realtimePriceHandler.QueryRealtimePrice(c.Request.Context(), &realtimePriceReq)
	if err != nil {
		h.logger.Error("实时价格查询失败",
			zap.String("username", req.Username),
			zap.Error(err))
		// 智能错误处理：区分不同类型的错误
		statusCode, message := h.categorizeError(err)
		h.sendError(c, statusCode, message, err.Error())
		return
	}

	// 记录查询成功
	h.logger.Info("实时价格查询成功",
		zap.String("username", req.Username),
		zap.Bool("success", resp.Success),
		zap.Int("total_prices", func() int {
			if resp.Data != nil {
				return len(resp.Data)
			}
			return 0
		}()))

	// 发送成功响应
	h.sendSuccess(c, resp)
}

// parseCainiaoSimpleOrderCode 解析菜鸟简单格式订单代码（如：CAINIAO_**********_YUNDA_1752581958）
func (h *UnifiedGatewayHandler) parseCainiaoSimpleOrderCode(orderCode string) (expressCode, originalCode, provider, channelID, productCode string, err error) {
	// 格式：CAINIAO_产品代码_快递公司代码_时间戳
	parts := strings.Split(orderCode, "_")
	if len(parts) < 4 {
		return "", "", "", "", "", fmt.Errorf("菜鸟订单代码格式错误，期望格式：CAINIAO_产品代码_快递公司代码_时间戳")
	}

	// 解析字段
	productCode = parts[1]                             // 产品代码（如：**********）
	expressCode = parts[2]                             // 快递公司代码（如：YUNDA、NORMAL等）
	originalCode = expressCode                         // 原始代码与快递代码相同
	provider = "cainiao"                               // 固定为菜鸟供应商
	channelID = fmt.Sprintf("cainiao_%s", expressCode) // 渠道ID格式：cainiao_YUNDA

	h.logger.Info("解析菜鸟简单订单代码",
		zap.String("order_code", orderCode),
		zap.String("express_code", expressCode),
		zap.String("original_code", originalCode),
		zap.String("provider", provider),
		zap.String("channel_id", channelID),
		zap.String("product_code", productCode))

	return expressCode, originalCode, provider, channelID, productCode, nil
}

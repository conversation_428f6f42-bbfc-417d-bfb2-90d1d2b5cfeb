package express

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// PriceInterfaceAllocator 查价接口分配器接口
type PriceInterfaceAllocator interface {
	// GetPriceInterface 获取快递公司的查价接口类型
	GetPriceInterface(ctx context.Context, companyCode string) (PriceInterfaceType, error)

	// GetPriceInterfaces 批量获取多个快递公司的接口类型
	GetPriceInterfaces(ctx context.Context, companyCodes []string) (map[string]PriceInterfaceType, error)

	// GetAllocationStats 获取接口分配统计信息
	GetAllocationStats(ctx context.Context) (*AllocationStats, error)

	// SetPriceInterface 设置快递公司的查价接口类型
	SetPriceInterface(ctx context.Context, companyCode string, interfaceType PriceInterfaceType, operatorID string) error

	// GetAllAllocations 获取所有快递公司的接口分配信息
	GetAllAllocations(ctx context.Context) ([]PriceInterfaceAllocation, error)
}

// DefaultPriceInterfaceAllocator 默认查价接口分配器实现
type DefaultPriceInterfaceAllocator struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewPriceInterfaceAllocator 创建新的查价接口分配器
func NewPriceInterfaceAllocator(db *sql.DB, logger *zap.Logger) PriceInterfaceAllocator {
	return &DefaultPriceInterfaceAllocator{
		db:     db,
		logger: logger,
	}
}

// GetPriceInterface 获取快递公司的查价接口类型
func (a *DefaultPriceInterfaceAllocator) GetPriceInterface(ctx context.Context, companyCode string) (PriceInterfaceType, error) {
	if companyCode == "" {
		return "", fmt.Errorf("快递公司代码不能为空")
	}

	// 查询快递公司的接口分配配置
	// 优先级：price_query_interface > interface_type（向下兼容）
	query := `
		SELECT 
			c.code,
			c.name,
			COALESCE(
				(SELECT config_value FROM express_company_configs 
				 WHERE company_id = c.id AND config_key = 'price_query_interface' AND is_active = true),
				(SELECT 
					CASE 
						WHEN config_value = 'dedicated' THEN 'QUERY_REALTIME_PRICE'
						WHEN config_value = 'unified' THEN 'QUERY_PRICE'
						ELSE config_value
					END
				 FROM express_company_configs 
				 WHERE company_id = c.id AND config_key = 'interface_type' AND is_active = true)
			) as interface_type
		FROM express_companies c
		WHERE c.code = $1 AND c.is_active = true
	`

	var companyName string
	var interfaceTypeStr sql.NullString

	err := a.db.QueryRowContext(ctx, query, companyCode).Scan(
		&companyCode, &companyName, &interfaceTypeStr,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return "", fmt.Errorf("快递公司不存在或已禁用: %s", companyCode)
		}
		a.logger.Error("查询快递公司接口配置失败",
			zap.String("company_code", companyCode),
			zap.Error(err))
		return "", fmt.Errorf("查询快递公司接口配置失败: %w", err)
	}

	// 如果没有配置接口类型，返回错误（必须明确分配）
	if !interfaceTypeStr.Valid || interfaceTypeStr.String == "" {
		return "", fmt.Errorf("快递公司 %s 未配置查价接口类型，请联系管理员配置", companyCode)
	}

	interfaceType := PriceInterfaceType(interfaceTypeStr.String)

	// 验证接口类型是否有效
	if !interfaceType.IsValid() {
		a.logger.Warn("快递公司配置了无效的接口类型",
			zap.String("company_code", companyCode),
			zap.String("interface_type", interfaceTypeStr.String))
		return "", fmt.Errorf("快递公司 %s 配置了无效的接口类型: %s", companyCode, interfaceTypeStr.String)
	}

	a.logger.Debug("获取快递公司接口类型成功",
		zap.String("company_code", companyCode),
		zap.String("company_name", companyName),
		zap.String("interface_type", string(interfaceType)))

	return interfaceType, nil
}

// GetPriceInterfaces 批量获取多个快递公司的接口类型
func (a *DefaultPriceInterfaceAllocator) GetPriceInterfaces(ctx context.Context, companyCodes []string) (map[string]PriceInterfaceType, error) {
	if len(companyCodes) == 0 {
		return make(map[string]PriceInterfaceType), nil
	}

	result := make(map[string]PriceInterfaceType)

	// 为了简化实现且避免复杂的SQL IN查询，这里使用循环调用
	// 在高并发场景下可以考虑优化为单次查询
	for _, code := range companyCodes {
		interfaceType, err := a.GetPriceInterface(ctx, code)
		if err != nil {
			a.logger.Warn("获取快递公司接口类型失败",
				zap.String("company_code", code),
				zap.Error(err))
			// 继续处理其他快递公司，不因单个失败而中断
			continue
		}
		result[code] = interfaceType
	}

	a.logger.Info("批量获取快递公司接口类型完成",
		zap.Int("requested_count", len(companyCodes)),
		zap.Int("success_count", len(result)))

	return result, nil
}

// GetAllocationStats 获取接口分配统计信息
func (a *DefaultPriceInterfaceAllocator) GetAllocationStats(ctx context.Context) (*AllocationStats, error) {
	// 查询所有快递公司的接口分配情况
	query := `
		SELECT 
			c.code,
			c.name,
			COALESCE(
				(SELECT config_value FROM express_company_configs 
				 WHERE company_id = c.id AND config_key = 'price_query_interface' AND is_active = true),
				(SELECT 
					CASE 
						WHEN config_value = 'dedicated' THEN 'QUERY_REALTIME_PRICE'
						WHEN config_value = 'unified' THEN 'QUERY_PRICE'
						ELSE config_value
					END
				 FROM express_company_configs 
				 WHERE company_id = c.id AND config_key = 'interface_type' AND is_active = true)
			) as interface_type,
			COALESCE(
				(SELECT 'price_query_interface' FROM express_company_configs 
				 WHERE company_id = c.id AND config_key = 'price_query_interface' AND is_active = true),
				(SELECT 'interface_type' FROM express_company_configs 
				 WHERE company_id = c.id AND config_key = 'interface_type' AND is_active = true)
			) as config_source,
			c.updated_at
		FROM express_companies c
		WHERE c.is_active = true
		ORDER BY c.code
	`

	rows, err := a.db.QueryContext(ctx, query)
	if err != nil {
		a.logger.Error("查询接口分配统计失败", zap.Error(err))
		return nil, fmt.Errorf("查询接口分配统计失败: %w", err)
	}
	defer rows.Close()

	stats := &AllocationStats{
		AllocationDetails:    make(map[string]PriceInterfaceAllocation),
		UnallocatedCompanies: make([]string, 0),
		LastRefreshed:        time.Now(),
	}

	for rows.Next() {
		var companyCode, companyName string
		var interfaceTypeStr, configSource sql.NullString
		var lastUpdated time.Time

		err := rows.Scan(&companyCode, &companyName, &interfaceTypeStr, &configSource, &lastUpdated)
		if err != nil {
			a.logger.Error("扫描接口分配数据失败", zap.Error(err))
			continue
		}

		stats.TotalCompanies++

		if !interfaceTypeStr.Valid || interfaceTypeStr.String == "" {
			// 未分配接口类型的快递公司
			stats.UnallocatedCompanies = append(stats.UnallocatedCompanies, companyCode)
		} else {
			interfaceType := PriceInterfaceType(interfaceTypeStr.String)
			if interfaceType.IsValid() {
				// 统计接口类型分布
				switch interfaceType {
				case PriceInterfaceStandard:
					stats.StandardInterface++
				case PriceInterfaceRealtime:
					stats.RealtimeInterface++
				}

				// 记录详细分配信息
				stats.AllocationDetails[companyCode] = PriceInterfaceAllocation{
					CompanyCode:   companyCode,
					CompanyName:   companyName,
					InterfaceType: interfaceType,
					ConfigSource:  configSource.String,
					LastUpdated:   lastUpdated,
				}
			} else {
				// 配置了无效接口类型的快递公司也算作未分配
				stats.UnallocatedCompanies = append(stats.UnallocatedCompanies, companyCode)
			}
		}
	}

	a.logger.Info("接口分配统计完成",
		zap.Int("total_companies", stats.TotalCompanies),
		zap.Int("standard_interface", stats.StandardInterface),
		zap.Int("realtime_interface", stats.RealtimeInterface),
		zap.Int("unallocated_companies", len(stats.UnallocatedCompanies)))

	return stats, nil
}

// SetPriceInterface 设置快递公司的查价接口类型
func (a *DefaultPriceInterfaceAllocator) SetPriceInterface(ctx context.Context, companyCode string, interfaceType PriceInterfaceType, operatorID string) error {
	if companyCode == "" {
		return fmt.Errorf("快递公司代码不能为空")
	}

	if !interfaceType.IsValid() {
		return fmt.Errorf("无效的接口类型: %s", interfaceType)
	}

	// 开始事务
	tx, err := a.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 查询快递公司是否存在
	var companyID, companyName string
	err = tx.QueryRowContext(ctx,
		"SELECT id, name FROM express_companies WHERE code = $1 AND is_active = true",
		companyCode).Scan(&companyID, &companyName)
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("快递公司不存在或已禁用: %s", companyCode)
		}
		return fmt.Errorf("查询快递公司失败: %w", err)
	}

	// 检查是否已存在 price_query_interface 配置
	var existingConfigID string
	err = tx.QueryRowContext(ctx,
		"SELECT id FROM express_company_configs WHERE company_id = $1 AND config_key = 'price_query_interface'",
		companyID).Scan(&existingConfigID)

	now := time.Now()

	if err == sql.ErrNoRows {
		// 创建新配置
		_, err = tx.ExecContext(ctx, `
			INSERT INTO express_company_configs (
				id, company_id, config_key, config_value, config_type,
				description, is_active, created_at, updated_at, created_by, updated_by
			) VALUES (gen_random_uuid(), $1, 'price_query_interface', $2, 'string',
					  '查价接口类型配置', true, $3, $4, $5, $6)`,
			companyID, string(interfaceType), now, now, operatorID, operatorID)
		if err != nil {
			return fmt.Errorf("创建接口配置失败: %w", err)
		}

		a.logger.Info("创建快递公司接口配置",
			zap.String("company_code", companyCode),
			zap.String("company_name", companyName),
			zap.String("interface_type", string(interfaceType)),
			zap.String("operator_id", operatorID))
	} else if err != nil {
		return fmt.Errorf("查询现有配置失败: %w", err)
	} else {
		// 更新现有配置
		_, err = tx.ExecContext(ctx, `
			UPDATE express_company_configs
			SET config_value = $1, updated_at = $2, updated_by = $3
			WHERE id = $4`,
			string(interfaceType), now, operatorID, existingConfigID)
		if err != nil {
			return fmt.Errorf("更新接口配置失败: %w", err)
		}

		a.logger.Info("更新快递公司接口配置",
			zap.String("company_code", companyCode),
			zap.String("company_name", companyName),
			zap.String("interface_type", string(interfaceType)),
			zap.String("operator_id", operatorID))
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// GetAllAllocations 获取所有快递公司的接口分配信息
func (a *DefaultPriceInterfaceAllocator) GetAllAllocations(ctx context.Context) ([]PriceInterfaceAllocation, error) {
	query := `
		SELECT
			c.code,
			c.name,
			COALESCE(
				(SELECT config_value FROM express_company_configs
				 WHERE company_id = c.id AND config_key = 'price_query_interface' AND is_active = true),
				(SELECT
					CASE
						WHEN config_value = 'dedicated' THEN 'QUERY_REALTIME_PRICE'
						WHEN config_value = 'unified' THEN 'QUERY_PRICE'
						ELSE config_value
					END
				 FROM express_company_configs
				 WHERE company_id = c.id AND config_key = 'interface_type' AND is_active = true)
			) as interface_type,
			COALESCE(
				(SELECT 'price_query_interface' FROM express_company_configs
				 WHERE company_id = c.id AND config_key = 'price_query_interface' AND is_active = true),
				(SELECT 'interface_type' FROM express_company_configs
				 WHERE company_id = c.id AND config_key = 'interface_type' AND is_active = true)
			) as config_source,
			COALESCE(
				(SELECT config_value::int FROM express_company_configs
				 WHERE company_id = c.id AND config_key = 'interface_priority' AND is_active = true),
				0
			) as priority,
			c.updated_at
		FROM express_companies c
		WHERE c.is_active = true
		ORDER BY c.code
	`

	rows, err := a.db.QueryContext(ctx, query)
	if err != nil {
		a.logger.Error("查询所有接口分配失败", zap.Error(err))
		return nil, fmt.Errorf("查询所有接口分配失败: %w", err)
	}
	defer rows.Close()

	var allocations []PriceInterfaceAllocation

	for rows.Next() {
		var companyCode, companyName string
		var interfaceTypeStr, configSource sql.NullString
		var priority int
		var lastUpdated time.Time

		err := rows.Scan(&companyCode, &companyName, &interfaceTypeStr, &configSource, &priority, &lastUpdated)
		if err != nil {
			a.logger.Error("扫描接口分配数据失败", zap.Error(err))
			continue
		}

		allocation := PriceInterfaceAllocation{
			CompanyCode:  companyCode,
			CompanyName:  companyName,
			Priority:     priority,
			ConfigSource: configSource.String,
			LastUpdated:  lastUpdated,
		}

		if interfaceTypeStr.Valid && interfaceTypeStr.String != "" {
			interfaceType := PriceInterfaceType(interfaceTypeStr.String)
			if interfaceType.IsValid() {
				allocation.InterfaceType = interfaceType
			}
		}

		allocations = append(allocations, allocation)
	}

	a.logger.Info("获取所有接口分配完成",
		zap.Int("total_allocations", len(allocations)))

	return allocations, nil
}

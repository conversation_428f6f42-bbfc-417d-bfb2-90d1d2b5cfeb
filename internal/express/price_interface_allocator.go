package express

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// PriceInterfaceAllocator 查价接口分配器接口
type PriceInterfaceAllocator interface {
	// GetPriceInterface 获取供应商+快递公司的查价接口类型
	GetPriceInterface(ctx context.Context, providerCode, companyCode string) (PriceInterfaceType, error)

	// GetPriceInterfaces 批量获取多个供应商+快递公司的接口类型
	GetPriceInterfaces(ctx context.Context, requests []ProviderCompanyRequest) (map[string]PriceInterfaceType, error)

	// GetAllocationStats 获取接口分配统计信息
	GetAllocationStats(ctx context.Context) (*AllocationStats, error)

	// SetPriceInterface 设置供应商+快递公司的查价接口类型
	SetPriceInterface(ctx context.Context, providerCode, companyCode string, interfaceType PriceInterfaceType, operatorID string) error

	// GetAllAllocations 获取所有供应商+快递公司的接口分配信息
	GetAllAllocations(ctx context.Context) ([]PriceInterfaceAllocation, error)

	// GetProviderAllocations 获取指定供应商的所有接口分配
	GetProviderAllocations(ctx context.Context, providerCode string) ([]PriceInterfaceAllocation, error)

	// GetCompanyAllocations 获取指定快递公司在所有供应商中的接口分配
	GetCompanyAllocations(ctx context.Context, companyCode string) ([]PriceInterfaceAllocation, error)
}

// ProviderCompanyRequest 供应商+快递公司请求
type ProviderCompanyRequest struct {
	ProviderCode string `json:"provider_code"`
	CompanyCode  string `json:"company_code"`
}

// DefaultPriceInterfaceAllocator 默认查价接口分配器实现
type DefaultPriceInterfaceAllocator struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewPriceInterfaceAllocator 创建新的查价接口分配器
func NewPriceInterfaceAllocator(db *sql.DB, logger *zap.Logger) PriceInterfaceAllocator {
	return &DefaultPriceInterfaceAllocator{
		db:     db,
		logger: logger,
	}
}

// GetPriceInterface 获取供应商+快递公司的查价接口类型
func (a *DefaultPriceInterfaceAllocator) GetPriceInterface(ctx context.Context, providerCode, companyCode string) (PriceInterfaceType, error) {
	if providerCode == "" {
		return "", fmt.Errorf("供应商代码不能为空")
	}
	if companyCode == "" {
		return "", fmt.Errorf("快递公司代码不能为空")
	}

	// 查询供应商+快递公司的接口分配配置
	query := `
		SELECT
			provider_code,
			provider_name,
			company_code,
			company_name,
			interface_type
		FROM provider_company_interface_allocations
		WHERE provider_code = $1 AND company_code = $2 AND is_active = true
	`

	var allocation PriceInterfaceAllocation
	var interfaceTypeStr string

	err := a.db.QueryRowContext(ctx, query, providerCode, companyCode).Scan(
		&allocation.ProviderCode,
		&allocation.ProviderName,
		&allocation.CompanyCode,
		&allocation.CompanyName,
		&interfaceTypeStr,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			// 如果新表中没有配置，降级到旧的快递公司级别配置
			return a.getFallbackInterface(ctx, companyCode)
		}
		a.logger.Error("查询供应商快递公司接口配置失败",
			zap.String("provider_code", providerCode),
			zap.String("company_code", companyCode),
			zap.Error(err))
		return "", fmt.Errorf("查询供应商快递公司接口配置失败: %w", err)
	}

	interfaceType := PriceInterfaceType(interfaceTypeStr)

	// 验证接口类型是否有效
	if !interfaceType.IsValid() {
		a.logger.Warn("供应商快递公司配置了无效的接口类型",
			zap.String("provider_code", providerCode),
			zap.String("company_code", companyCode),
			zap.String("interface_type", interfaceTypeStr))
		return "", fmt.Errorf("供应商 %s 快递公司 %s 配置了无效的接口类型: %s", providerCode, companyCode, interfaceTypeStr)
	}

	a.logger.Debug("获取供应商快递公司接口类型成功",
		zap.String("provider_code", providerCode),
		zap.String("provider_name", allocation.ProviderName),
		zap.String("company_code", companyCode),
		zap.String("company_name", allocation.CompanyName),
		zap.String("interface_type", string(interfaceType)))

	return interfaceType, nil
}

// getFallbackInterface 降级到旧的快递公司级别配置
func (a *DefaultPriceInterfaceAllocator) getFallbackInterface(ctx context.Context, companyCode string) (PriceInterfaceType, error) {
	// 查询快递公司的接口分配配置（旧逻辑）
	query := `
		SELECT
			COALESCE(
				(SELECT config_value FROM express_company_configs
				 WHERE company_id = c.id AND config_key = 'price_query_interface' AND is_active = true),
				(SELECT
					CASE
						WHEN config_value = 'dedicated' THEN 'QUERY_REALTIME_PRICE'
						WHEN config_value = 'unified' THEN 'QUERY_PRICE'
						ELSE config_value
					END
				 FROM express_company_configs
				 WHERE company_id = c.id AND config_key = 'interface_type' AND is_active = true)
			) as interface_type
		FROM express_companies c
		WHERE c.code = $1 AND c.is_active = true
	`

	var interfaceTypeStr sql.NullString

	err := a.db.QueryRowContext(ctx, query, companyCode).Scan(&interfaceTypeStr)

	if err != nil {
		if err == sql.ErrNoRows {
			return "", fmt.Errorf("快递公司不存在或已禁用: %s", companyCode)
		}
		return "", fmt.Errorf("查询快递公司接口配置失败: %w", err)
	}

	// 如果没有配置接口类型，返回错误（必须明确分配）
	if !interfaceTypeStr.Valid || interfaceTypeStr.String == "" {
		return "", fmt.Errorf("快递公司 %s 未配置查价接口类型，请联系管理员配置", companyCode)
	}

	interfaceType := PriceInterfaceType(interfaceTypeStr.String)

	// 验证接口类型是否有效
	if !interfaceType.IsValid() {
		return "", fmt.Errorf("快递公司 %s 配置了无效的接口类型: %s", companyCode, interfaceTypeStr.String)
	}

	a.logger.Debug("使用降级配置获取快递公司接口类型",
		zap.String("company_code", companyCode),
		zap.String("interface_type", string(interfaceType)))

	return interfaceType, nil
}

// GetPriceInterfaces 批量获取多个供应商+快递公司的接口类型
func (a *DefaultPriceInterfaceAllocator) GetPriceInterfaces(ctx context.Context, requests []ProviderCompanyRequest) (map[string]PriceInterfaceType, error) {
	if len(requests) == 0 {
		return make(map[string]PriceInterfaceType), nil
	}

	result := make(map[string]PriceInterfaceType)

	// 为了简化实现且避免复杂的SQL IN查询，这里使用循环调用
	// 在高并发场景下可以考虑优化为单次查询
	for _, req := range requests {
		interfaceType, err := a.GetPriceInterface(ctx, req.ProviderCode, req.CompanyCode)
		if err != nil {
			a.logger.Warn("获取供应商快递公司接口类型失败",
				zap.String("provider_code", req.ProviderCode),
				zap.String("company_code", req.CompanyCode),
				zap.Error(err))
			// 继续处理其他组合，不因单个失败而中断
			continue
		}
		key := fmt.Sprintf("%s:%s", req.ProviderCode, req.CompanyCode)
		result[key] = interfaceType
	}

	a.logger.Info("批量获取供应商快递公司接口类型完成",
		zap.Int("requested_count", len(requests)),
		zap.Int("success_count", len(result)))

	return result, nil
}

// GetAllocationStats 获取接口分配统计信息
func (a *DefaultPriceInterfaceAllocator) GetAllocationStats(ctx context.Context) (*AllocationStats, error) {
	// 查询所有供应商+快递公司的接口分配情况
	query := `
		SELECT
			pcia.provider_code,
			pcia.provider_name,
			pcia.company_code,
			pcia.company_name,
			pcia.interface_type,
			pcia.priority,
			pcia.description,
			pcia.is_active,
			pcia.updated_at
		FROM provider_company_interface_allocations pcia
		WHERE pcia.is_active = true
		ORDER BY pcia.provider_code, pcia.company_code
	`

	rows, err := a.db.QueryContext(ctx, query)
	if err != nil {
		a.logger.Error("查询接口分配统计失败", zap.Error(err))
		return nil, fmt.Errorf("查询接口分配统计失败: %w", err)
	}
	defer rows.Close()

	stats := &AllocationStats{
		AllocationDetails:   make(map[string]PriceInterfaceAllocation),
		ProviderStats:       make(map[string]ProviderAllocationStats),
		UnallocatedMappings: make([]UnallocatedMapping, 0),
		LastRefreshed:       time.Now(),
	}

	for rows.Next() {
		var allocation PriceInterfaceAllocation
		var description sql.NullString

		err := rows.Scan(
			&allocation.ProviderCode,
			&allocation.ProviderName,
			&allocation.CompanyCode,
			&allocation.CompanyName,
			&allocation.InterfaceType,
			&allocation.Priority,
			&description,
			&allocation.IsActive,
			&allocation.LastUpdated,
		)
		if err != nil {
			a.logger.Error("扫描接口分配数据失败", zap.Error(err))
			continue
		}

		if description.Valid {
			allocation.ConfigSource = description.String
		}

		stats.TotalAllocations++

		// 统计接口类型分布
		switch allocation.InterfaceType {
		case PriceInterfaceStandard:
			stats.StandardInterface++
		case PriceInterfaceRealtime:
			stats.RealtimeInterface++
		}

		// 按供应商统计
		providerStat, exists := stats.ProviderStats[allocation.ProviderCode]
		if !exists {
			providerStat = ProviderAllocationStats{
				ProviderCode: allocation.ProviderCode,
				ProviderName: allocation.ProviderName,
			}
		}
		providerStat.TotalMappings++
		switch allocation.InterfaceType {
		case PriceInterfaceStandard:
			providerStat.StandardInterface++
		case PriceInterfaceRealtime:
			providerStat.RealtimeInterface++
		}
		stats.ProviderStats[allocation.ProviderCode] = providerStat

		// 记录详细分配信息
		key := fmt.Sprintf("%s:%s", allocation.ProviderCode, allocation.CompanyCode)
		stats.AllocationDetails[key] = allocation
	}

	a.logger.Info("接口分配统计完成",
		zap.Int("total_allocations", stats.TotalAllocations),
		zap.Int("standard_interface", stats.StandardInterface),
		zap.Int("realtime_interface", stats.RealtimeInterface),
		zap.Int("provider_count", len(stats.ProviderStats)))

	return stats, nil
}

// SetPriceInterface 设置供应商+快递公司的查价接口类型
func (a *DefaultPriceInterfaceAllocator) SetPriceInterface(ctx context.Context, providerCode, companyCode string, interfaceType PriceInterfaceType, operatorID string) error {
	if providerCode == "" {
		return fmt.Errorf("供应商代码不能为空")
	}
	if companyCode == "" {
		return fmt.Errorf("快递公司代码不能为空")
	}
	if !interfaceType.IsValid() {
		return fmt.Errorf("无效的接口类型: %s", interfaceType)
	}

	// 开始事务
	tx, err := a.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 查询供应商和快递公司是否存在映射关系
	var providerName, companyName string
	err = tx.QueryRowContext(ctx, `
		SELECT p.name, c.name
		FROM express_mappings em
		JOIN express_providers p ON em.provider_id = p.id
		JOIN express_companies c ON em.company_id = c.id
		WHERE p.code = $1 AND c.code = $2 AND p.is_active = true AND c.is_active = true AND em.is_active = true
	`, providerCode, companyCode).Scan(&providerName, &companyName)

	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("供应商 %s 不支持快递公司 %s 或映射关系已禁用", providerCode, companyCode)
		}
		return fmt.Errorf("查询供应商快递公司映射失败: %w", err)
	}

	// 检查是否已存在分配配置
	var existingID string
	err = tx.QueryRowContext(ctx,
		"SELECT id FROM provider_company_interface_allocations WHERE provider_code = $1 AND company_code = $2",
		providerCode, companyCode).Scan(&existingID)

	now := time.Now()

	if err == sql.ErrNoRows {
		// 创建新配置
		_, err = tx.ExecContext(ctx, `
			INSERT INTO provider_company_interface_allocations (
				provider_code, provider_name, company_code, company_name,
				interface_type, priority, description, is_active,
				created_at, updated_at, created_by, updated_by
			) VALUES ($1, $2, $3, $4, $5, 0, '管理员手动配置', true, $6, $7, $8, $9)`,
			providerCode, providerName, companyCode, companyName,
			string(interfaceType), now, now, operatorID, operatorID)
		if err != nil {
			return fmt.Errorf("创建接口配置失败: %w", err)
		}

		a.logger.Info("创建供应商快递公司接口配置",
			zap.String("provider_code", providerCode),
			zap.String("provider_name", providerName),
			zap.String("company_code", companyCode),
			zap.String("company_name", companyName),
			zap.String("interface_type", string(interfaceType)),
			zap.String("operator_id", operatorID))
	} else if err != nil {
		return fmt.Errorf("查询现有配置失败: %w", err)
	} else {
		// 更新现有配置
		_, err = tx.ExecContext(ctx, `
			UPDATE provider_company_interface_allocations
			SET interface_type = $1, updated_at = $2, updated_by = $3, description = '管理员手动更新'
			WHERE id = $4`,
			string(interfaceType), now, operatorID, existingID)
		if err != nil {
			return fmt.Errorf("更新接口配置失败: %w", err)
		}

		a.logger.Info("更新供应商快递公司接口配置",
			zap.String("provider_code", providerCode),
			zap.String("provider_name", providerName),
			zap.String("company_code", companyCode),
			zap.String("company_name", companyName),
			zap.String("interface_type", string(interfaceType)),
			zap.String("operator_id", operatorID))
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// GetAllAllocations 获取所有供应商+快递公司的接口分配信息
func (a *DefaultPriceInterfaceAllocator) GetAllAllocations(ctx context.Context) ([]PriceInterfaceAllocation, error) {
	query := `
		SELECT
			provider_code,
			provider_name,
			company_code,
			company_name,
			interface_type,
			priority,
			description,
			is_active,
			updated_at
		FROM provider_company_interface_allocations
		WHERE is_active = true
		ORDER BY provider_code, company_code
	`

	rows, err := a.db.QueryContext(ctx, query)
	if err != nil {
		a.logger.Error("查询所有接口分配失败", zap.Error(err))
		return nil, fmt.Errorf("查询所有接口分配失败: %w", err)
	}
	defer rows.Close()

	var allocations []PriceInterfaceAllocation

	for rows.Next() {
		var allocation PriceInterfaceAllocation
		var description sql.NullString

		err := rows.Scan(
			&allocation.ProviderCode,
			&allocation.ProviderName,
			&allocation.CompanyCode,
			&allocation.CompanyName,
			&allocation.InterfaceType,
			&allocation.Priority,
			&description,
			&allocation.IsActive,
			&allocation.LastUpdated,
		)
		if err != nil {
			a.logger.Error("扫描接口分配数据失败", zap.Error(err))
			continue
		}

		if description.Valid {
			allocation.ConfigSource = description.String
		}

		allocations = append(allocations, allocation)
	}

	a.logger.Info("获取所有接口分配完成",
		zap.Int("total_allocations", len(allocations)))

	return allocations, nil
}

// GetProviderAllocations 获取指定供应商的所有接口分配
func (a *DefaultPriceInterfaceAllocator) GetProviderAllocations(ctx context.Context, providerCode string) ([]PriceInterfaceAllocation, error) {
	if providerCode == "" {
		return nil, fmt.Errorf("供应商代码不能为空")
	}

	query := `
		SELECT
			provider_code,
			provider_name,
			company_code,
			company_name,
			interface_type,
			priority,
			description,
			is_active,
			updated_at
		FROM provider_company_interface_allocations
		WHERE provider_code = $1 AND is_active = true
		ORDER BY company_code
	`

	rows, err := a.db.QueryContext(ctx, query, providerCode)
	if err != nil {
		a.logger.Error("查询供应商接口分配失败", zap.Error(err))
		return nil, fmt.Errorf("查询供应商接口分配失败: %w", err)
	}
	defer rows.Close()

	var allocations []PriceInterfaceAllocation

	for rows.Next() {
		var allocation PriceInterfaceAllocation
		var description sql.NullString

		err := rows.Scan(
			&allocation.ProviderCode,
			&allocation.ProviderName,
			&allocation.CompanyCode,
			&allocation.CompanyName,
			&allocation.InterfaceType,
			&allocation.Priority,
			&description,
			&allocation.IsActive,
			&allocation.LastUpdated,
		)
		if err != nil {
			a.logger.Error("扫描供应商接口分配数据失败", zap.Error(err))
			continue
		}

		if description.Valid {
			allocation.ConfigSource = description.String
		}

		allocations = append(allocations, allocation)
	}

	a.logger.Info("获取供应商接口分配完成",
		zap.String("provider_code", providerCode),
		zap.Int("total_allocations", len(allocations)))

	return allocations, nil
}

// GetCompanyAllocations 获取指定快递公司在所有供应商中的接口分配
func (a *DefaultPriceInterfaceAllocator) GetCompanyAllocations(ctx context.Context, companyCode string) ([]PriceInterfaceAllocation, error) {
	if companyCode == "" {
		return nil, fmt.Errorf("快递公司代码不能为空")
	}

	query := `
		SELECT
			provider_code,
			provider_name,
			company_code,
			company_name,
			interface_type,
			priority,
			description,
			is_active,
			updated_at
		FROM provider_company_interface_allocations
		WHERE company_code = $1 AND is_active = true
		ORDER BY provider_code
	`

	rows, err := a.db.QueryContext(ctx, query, companyCode)
	if err != nil {
		a.logger.Error("查询快递公司接口分配失败", zap.Error(err))
		return nil, fmt.Errorf("查询快递公司接口分配失败: %w", err)
	}
	defer rows.Close()

	var allocations []PriceInterfaceAllocation

	for rows.Next() {
		var allocation PriceInterfaceAllocation
		var description sql.NullString

		err := rows.Scan(
			&allocation.ProviderCode,
			&allocation.ProviderName,
			&allocation.CompanyCode,
			&allocation.CompanyName,
			&allocation.InterfaceType,
			&allocation.Priority,
			&description,
			&allocation.IsActive,
			&allocation.LastUpdated,
		)
		if err != nil {
			a.logger.Error("扫描快递公司接口分配数据失败", zap.Error(err))
			continue
		}

		if description.Valid {
			allocation.ConfigSource = description.String
		}

		allocations = append(allocations, allocation)
	}

	a.logger.Info("获取快递公司接口分配完成",
		zap.String("company_code", companyCode),
		zap.Int("total_allocations", len(allocations)))

	return allocations, nil
}

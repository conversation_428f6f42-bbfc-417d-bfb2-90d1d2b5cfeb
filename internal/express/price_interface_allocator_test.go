package express

import (
	"context"
	"database/sql"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"
)

func TestDefaultPriceInterfaceAllocator_GetPriceInterface(t *testing.T) {
	logger := zaptest.NewLogger(t)

	tests := []struct {
		name          string
		companyCode   string
		mockSetup     func(mock sqlmock.Sqlmock)
		expectedType  PriceInterfaceType
		expectedError bool
		errorContains string
	}{
		{
			name:        "获取标准查价接口配置",
			companyCode: "YTO",
			mockSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"code", "name", "interface_type"}).
					AddRow("YTO", "圆通速递", "QUERY_PRICE")
				mock.ExpectQuery(`SELECT.*FROM express_companies.*WHERE.*code.*`).
					WithArgs("YTO").
					WillReturnRows(rows)
			},
			expectedType:  PriceInterfaceStandard,
			expectedError: false,
		},
		{
			name:        "获取实时查价接口配置",
			companyCode: "JD",
			mockSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"code", "name", "interface_type"}).
					AddRow("JD", "京东快递", "QUERY_REALTIME_PRICE")
				mock.ExpectQuery(`SELECT.*FROM express_companies.*WHERE.*code.*`).
					WithArgs("JD").
					WillReturnRows(rows)
			},
			expectedType:  PriceInterfaceRealtime,
			expectedError: false,
		},
		{
			name:        "向下兼容旧配置unified",
			companyCode: "SF",
			mockSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"code", "name", "interface_type"}).
					AddRow("SF", "顺丰速运", "QUERY_PRICE")
				mock.ExpectQuery(`SELECT.*FROM express_companies.*WHERE.*code.*`).
					WithArgs("SF").
					WillReturnRows(rows)
			},
			expectedType:  PriceInterfaceStandard,
			expectedError: false,
		},
		{
			name:        "向下兼容旧配置dedicated",
			companyCode: "DBL",
			mockSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"code", "name", "interface_type"}).
					AddRow("DBL", "德邦快递", "QUERY_REALTIME_PRICE")
				mock.ExpectQuery(`SELECT.*FROM express_companies.*WHERE.*code.*`).
					WithArgs("DBL").
					WillReturnRows(rows)
			},
			expectedType:  PriceInterfaceRealtime,
			expectedError: false,
		},
		{
			name:        "快递公司不存在",
			companyCode: "INVALID",
			mockSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(`SELECT.*FROM express_companies.*WHERE.*code.*`).
					WithArgs("INVALID").
					WillReturnError(sql.ErrNoRows)
			},
			expectedError: true,
			errorContains: "快递公司不存在或已禁用",
		},
		{
			name:        "未配置接口类型",
			companyCode: "UNCONFIGURED",
			mockSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"code", "name", "interface_type"}).
					AddRow("UNCONFIGURED", "未配置公司", nil)
				mock.ExpectQuery(`SELECT.*FROM express_companies.*WHERE.*code.*`).
					WithArgs("UNCONFIGURED").
					WillReturnRows(rows)
			},
			expectedError: true,
			errorContains: "未配置查价接口类型",
		},
		{
			name:          "空的快递公司代码",
			companyCode:   "",
			mockSetup:     func(mock sqlmock.Sqlmock) {},
			expectedError: true,
			errorContains: "快递公司代码不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建模拟数据库
			db, mock, err := sqlmock.New()
			require.NoError(t, err)
			defer db.Close()

			// 设置模拟期望
			tt.mockSetup(mock)

			// 创建分配器
			allocator := NewPriceInterfaceAllocator(db, logger)

			// 执行测试
			ctx := context.Background()
			interfaceType, err := allocator.GetPriceInterface(ctx, tt.companyCode)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedType, interfaceType)
			}

			// 验证所有期望都被满足
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestDefaultPriceInterfaceAllocator_SetPriceInterface(t *testing.T) {
	logger := zaptest.NewLogger(t)

	tests := []struct {
		name          string
		companyCode   string
		interfaceType PriceInterfaceType
		operatorID    string
		mockSetup     func(mock sqlmock.Sqlmock)
		expectedError bool
		errorContains string
	}{
		{
			name:          "创建新的接口配置",
			companyCode:   "YTO",
			interfaceType: PriceInterfaceStandard,
			operatorID:    "admin",
			mockSetup: func(mock sqlmock.Sqlmock) {
				// 开始事务
				mock.ExpectBegin()

				// 查询快递公司
				companyRows := sqlmock.NewRows([]string{"id", "name"}).
					AddRow("company-id-1", "圆通速递")
				mock.ExpectQuery(`SELECT id, name FROM express_companies WHERE code.*`).
					WithArgs("YTO").
					WillReturnRows(companyRows)

				// 查询现有配置（不存在）
				mock.ExpectQuery(`SELECT id FROM express_company_configs WHERE company_id.*`).
					WithArgs("company-id-1").
					WillReturnError(sql.ErrNoRows)

				// 创建新配置
				mock.ExpectExec(`INSERT INTO express_company_configs.*`).
					WillReturnResult(sqlmock.NewResult(1, 1))

				// 提交事务
				mock.ExpectCommit()
			},
			expectedError: false,
		},
		{
			name:          "更新现有接口配置",
			companyCode:   "JD",
			interfaceType: PriceInterfaceRealtime,
			operatorID:    "admin",
			mockSetup: func(mock sqlmock.Sqlmock) {
				// 开始事务
				mock.ExpectBegin()

				// 查询快递公司
				companyRows := sqlmock.NewRows([]string{"id", "name"}).
					AddRow("company-id-2", "京东快递")
				mock.ExpectQuery(`SELECT id, name FROM express_companies WHERE code.*`).
					WithArgs("JD").
					WillReturnRows(companyRows)

				// 查询现有配置（存在）
				configRows := sqlmock.NewRows([]string{"id"}).
					AddRow("config-id-1")
				mock.ExpectQuery(`SELECT id FROM express_company_configs WHERE company_id.*`).
					WithArgs("company-id-2").
					WillReturnRows(configRows)

				// 更新配置
				mock.ExpectExec(`UPDATE express_company_configs.*`).
					WillReturnResult(sqlmock.NewResult(1, 1))

				// 提交事务
				mock.ExpectCommit()
			},
			expectedError: false,
		},
		{
			name:          "无效的接口类型",
			companyCode:   "SF",
			interfaceType: "INVALID_TYPE",
			operatorID:    "admin",
			mockSetup:     func(mock sqlmock.Sqlmock) {},
			expectedError: true,
			errorContains: "无效的接口类型",
		},
		{
			name:          "快递公司不存在",
			companyCode:   "INVALID",
			interfaceType: PriceInterfaceStandard,
			operatorID:    "admin",
			mockSetup: func(mock sqlmock.Sqlmock) {
				// 开始事务
				mock.ExpectBegin()

				// 查询快递公司（不存在）
				mock.ExpectQuery(`SELECT id, name FROM express_companies WHERE code.*`).
					WithArgs("INVALID").
					WillReturnError(sql.ErrNoRows)

				// 回滚事务
				mock.ExpectRollback()
			},
			expectedError: true,
			errorContains: "快递公司不存在或已禁用",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建模拟数据库
			db, mock, err := sqlmock.New()
			require.NoError(t, err)
			defer db.Close()

			// 设置模拟期望
			tt.mockSetup(mock)

			// 创建分配器
			allocator := NewPriceInterfaceAllocator(db, logger)

			// 执行测试
			ctx := context.Background()
			err = allocator.SetPriceInterface(ctx, tt.companyCode, tt.interfaceType, tt.operatorID)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}

			// 验证所有期望都被满足
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestPriceInterfaceType_IsValid(t *testing.T) {
	tests := []struct {
		name          string
		interfaceType PriceInterfaceType
		expected      bool
	}{
		{"标准查价接口", PriceInterfaceStandard, true},
		{"实时查价接口", PriceInterfaceRealtime, true},
		{"无效接口类型", "INVALID", false},
		{"空接口类型", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.interfaceType.IsValid()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestPriceInterfaceType_String(t *testing.T) {
	tests := []struct {
		name          string
		interfaceType PriceInterfaceType
		expected      string
	}{
		{"标准查价接口", PriceInterfaceStandard, "QUERY_PRICE"},
		{"实时查价接口", PriceInterfaceRealtime, "QUERY_REALTIME_PRICE"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.interfaceType.String()
			assert.Equal(t, tt.expected, result)
		})
	}
}

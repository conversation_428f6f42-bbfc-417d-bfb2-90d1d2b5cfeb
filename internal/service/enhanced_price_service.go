package service

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/shopspring/decimal"
	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/pool"
	"github.com/your-org/go-kuaidi/internal/util"
	"github.com/your-org/go-kuaidi/internal/utils"
	"go.uber.org/zap"
)

// EnhancedPriceService 增强版价格查询服务 (集成重量档位缓存)
// 职责: 在原有价格服务基础上集成重量档位缓存功能
// 遵循DRY原则，避免重复现有price_service.go的功能
type EnhancedPriceService struct {
	// 原有服务组件
	validator           *PriceValidator
	providerCoordinator *PriceProviderCoordinator
	workerPool          *pool.WorkerPool
	stats               *ServiceStats

	// 新增：重量档位缓存组件
	weightCacheService WeightTierCacheService
	configService      SystemConfigService
	volumeCalc         *utils.VolumeWeightCalculator
	logger             *zap.Logger

	// 🚀 新增：快递公司仓储
	expressCompanyRepo express.ExpressCompanyRepository

	// 🚀 新增：接口分配器
	interfaceAllocator express.PriceInterfaceAllocator

	// 🚀 新增：性能监控器
	performanceMonitor *PricePerformanceMonitor

	// 配置选项
	enableWeightCache bool
	maxWeightForCache float64 // 最大支持缓存的重量 (20KG)
}

// NewEnhancedPriceService 创建增强版价格查询服务
func NewEnhancedPriceService(
	providerManager *adapter.ProviderManager,
	mappingService express.ExpressMappingService,
	expressCompanyRepo express.ExpressCompanyRepository,
	weightCacheService WeightTierCacheService, // 🔥 修复：直接接受已配置的缓存服务实例
	configService SystemConfigService,
	blacklistService *RegionBlacklistService, // 🎯 新增：地区黑名单服务
	logger *zap.Logger,
) *EnhancedPriceService {
	// 初始化Worker Pool
	workerPool := pool.NewWorkerPool(20, 100)
	workerPool.Start()

	// 创建体积重量计算器
	volumeCalc := utils.NewVolumeWeightCalculator(logger, expressCompanyRepo)

	// 从数据库获取配置
	enableCache := true
	maxWeight := 20.0

	if configService != nil {
		enableCache, _ = configService.GetBoolConfig(context.Background(), "weight_tier_cache", "enabled", true)
		maxWeightInt, _ := configService.GetIntConfig(context.Background(), "weight_tier_cache", "max_cache_weight", 20)
		maxWeight = float64(maxWeightInt)
	}

	return &EnhancedPriceService{
		validator:           NewPriceValidator(logger, expressCompanyRepo),
		providerCoordinator: NewPriceProviderCoordinator(providerManager, mappingService, blacklistService),
		workerPool:          workerPool,
		stats:               &ServiceStats{},
		weightCacheService:  weightCacheService,
		configService:       configService,
		volumeCalc:          volumeCalc,
		expressCompanyRepo:  expressCompanyRepo,
		interfaceAllocator:  nil, // 通过SetInterfaceAllocator方法设置
		logger:              logger,
		performanceMonitor:  NewPricePerformanceMonitor(logger), // 🚀 新增：初始化性能监控器
		enableWeightCache:   enableCache,
		maxWeightForCache:   maxWeight,
	}
}

// SetInterfaceAllocator 设置接口分配器
func (s *EnhancedPriceService) SetInterfaceAllocator(allocator express.PriceInterfaceAllocator) {
	s.interfaceAllocator = allocator
}

// QueryPrice 统一查询价格 (增强版：集成重量档位缓存)
// 根据重量和查询模式自动选择最优策略：
// 1. 重量 <= 20KG 且单快递查询 → 使用重量档位缓存
// 2. 其他情况 → 使用原有实时查询逻辑
func (s *EnhancedPriceService) QueryPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	start := util.NowBeijing()

	// 验证请求参数
	if err := s.validator.ValidateRequest(req); err != nil {
		return &model.PriceResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: err.Error(),
		}, nil
	}

	// 🚀 新增：查价参数白名单过滤，只允许活跃且被供应商支持的快递公司
	activeCodes, _ := s.getActiveExpressCodes(ctx)
	activeSet := make(map[string]struct{})
	for _, code := range activeCodes {
		activeSet[code] = struct{}{}
	}
	var filtered []string
	for _, code := range req.ExpressCodes {
		if _, ok := activeSet[code]; ok {
			filtered = append(filtered, code)
		}
	}
	if len(filtered) == 0 && len(req.ExpressCodes) > 0 {
		return &model.PriceResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "所有请求的快递公司均已被禁用或不可用",
		}, nil
	}
	req.ExpressCodes = filtered

	// 预处理体积重量
	if s.validator.ShouldPreprocessVolumeWeight(req) {
		s.validator.PreprocessVolumeWeight(req)
	}

	s.logger.Info("接收到价格查询请求",
		zap.String("provider", req.Provider),
		zap.String("express_type", req.ExpressType),
		zap.Float64("weight", req.Package.Weight),
		zap.Bool("is_compare", req.IsCompare),
		zap.Bool("query_all", req.QueryAllCompanies),
		zap.Bool("disable_cache", req.DisableCache))

	// 判断是否可以使用重量档位缓存
	canUseWeightCache := s.shouldUseWeightCache(req)

	if canUseWeightCache {
		s.logger.Info("使用重量档位缓存查询")

		// 🚀 新增：根据查询类型选择缓存策略
		if req.QueryAllCompanies {
			return s.queryAllCompaniesWithCache(ctx, req, start)
		} else if req.IsCompare {
			return s.compareAllProvidersWithCache(ctx, req, start)
		} else if len(req.ExpressCodes) > 1 {
			return s.queryMultipleExpressWithCache(ctx, req, start)
		} else {
			// 单快递查询，使用原有缓存逻辑
			return s.queryWithWeightCache(ctx, req, start)
		}
	} else {
		s.logger.Info("使用实时查询",
			zap.String("reason", s.getRealtimeQueryReason(req)))
		return s.queryWithRealtimeAPI(ctx, req)
	}
}

// shouldUseWeightCache 判断是否应该使用重量档位缓存
func (s *EnhancedPriceService) shouldUseWeightCache(req *model.PriceRequest) bool {
	// 检查缓存是否启用
	if !s.enableWeightCache || req.DisableCache {
		return false
	}

	// 🔥 优先检查：如果请求涉及实时查价快递，整个请求都不使用缓存
	if s.isRealtimePriceNoCacheEnabled(req) {
		s.logger.Info("请求涉及实时查价快递，整个请求禁用缓存",
			zap.String("express_type", req.ExpressType),
			zap.Strings("express_codes", req.ExpressCodes))
		return false
	}

	// 注意：实时查价检查已经在isRealtimePriceNoCacheEnabled中处理了

	// 检查重量范围 (1-20KG)
	if req.Package.Weight <= 0 || req.Package.Weight > s.maxWeightForCache {
		return false
	}

	// 检查必要的地址信息
	if req.Sender.Province == "" || req.Receiver.Province == "" {
		return false
	}

	// 🚀 新增：支持批量查询缓存
	// 单快递查询：直接支持缓存
	if req.ExpressType != "" || len(req.ExpressCodes) == 1 {
		return true
	}

	// 多快递查询：支持缓存（每个快递单独缓存）
	if len(req.ExpressCodes) > 1 {
		return true
	}

	// 查询所有快递公司：支持缓存（每个快递单独缓存）
	if req.QueryAllCompanies {
		return true
	}

	// 价格比较模式：支持缓存（每个供应商的快递单独缓存）
	if req.IsCompare {
		return true
	}

	return false
}

// isRealtimePriceNoCacheEnabled 检查实时查价是否禁用缓存（基于接口分配器）
func (s *EnhancedPriceService) isRealtimePriceNoCacheEnabled(req *model.PriceRequest) bool {
	// 检查是否涉及实时查价快递
	isRealtimeExpress := false

	// 检查单个快递类型
	if req.ExpressType != "" {
		if s.checkExpressInterfaceType(req.ExpressType) {
			isRealtimeExpress = true
		}
	}

	// 检查快递代码列表
	if !isRealtimeExpress {
		for _, code := range req.ExpressCodes {
			if s.checkExpressInterfaceType(code) {
				isRealtimeExpress = true
				break
			}
		}
	}

	// 如果不涉及实时查价快递，返回false（不禁用缓存）
	if !isRealtimeExpress {
		return false
	}

	// 从数据库配置获取实时查价缓存禁用设置
	if s.configService != nil {
		ctx := context.Background()
		disableRealtimeCache, err := s.configService.GetBoolConfig(ctx, "price_cache", "realtime_price_disable_cache", true) // 默认true
		if err != nil {
			s.logger.Warn("获取实时查价缓存配置失败，为安全起见禁用缓存",
				zap.Error(err))
			return true // 配置获取失败时，安全起见禁用缓存
		}

		if disableRealtimeCache {
			s.logger.Info("实时查价缓存已禁用，将使用实时查询")
			return true
		}
	}

	return false
}

// checkExpressInterfaceType 检查单个快递公司的接口类型
func (s *EnhancedPriceService) checkExpressInterfaceType(expressCode string) bool {
	// 新的接口分配器需要供应商代码，但在这个上下文中我们没有供应商信息
	// 所以暂时使用旧的逻辑，后续可以通过重构来支持供应商级别的分配
	// TODO: 重构以支持供应商级别的接口分配
	return s.checkExpressInterfaceTypeLegacy(expressCode)
}

// checkExpressInterfaceTypeLegacy 旧的接口类型检查逻辑（向下兼容）
func (s *EnhancedPriceService) checkExpressInterfaceTypeLegacy(expressCode string) bool {
	if s.expressCompanyRepo == nil {
		return false
	}

	// 根据快递代码获取快递公司信息
	company, err := s.expressCompanyRepo.GetCompanyByCode(expressCode)
	if err != nil {
		s.logger.Debug("获取快递公司信息失败",
			zap.String("express_code", expressCode),
			zap.Error(err))
		return false
	}

	// 获取快递公司配置
	configs, err := s.expressCompanyRepo.GetConfigsByCompany(company.ID)
	if err != nil {
		s.logger.Debug("获取快递公司配置失败",
			zap.String("express_code", expressCode),
			zap.String("company_id", company.ID),
			zap.Error(err))
		return false
	}

	// 检查是否配置为dedicated接口
	for _, config := range configs {
		if config.ConfigKey == "interface_type" && config.ConfigValue == "dedicated" {
			s.logger.Info("快递公司配置为dedicated接口（旧逻辑）",
				zap.String("express_code", expressCode),
				zap.String("company_id", company.ID))
			return true
		}
	}

	return false
}

// isRealtimePriceOrder 检查是否是实时查价订单
func (s *EnhancedPriceService) isRealtimePriceOrder(req *model.OrderValidationRequest) bool {
	// 实时查价快递代码变体列表（包括京东、德邦、菜鸟、快递鸟等）
	realtimeCodes := []string{"JD", "jd", "jingdong", "JINGDONG", "京东", "京东快递", "jd_express", "DBL", "dbl", "debang", "DEBANG", "德邦", "德邦快递", "dbl_express", "CAINIAO", "cainiao", "菜鸟", "菜鸟裹裹", "KUAIDINIAO", "kuaidiniao", "快递鸟"}

	for _, realtimeCode := range realtimeCodes {
		if req.ExpressCode == realtimeCode {
			return true
		}
	}
	return false
}

// validateOrderPriceRealtime 使用实时API验证订单价格
func (s *EnhancedPriceService) validateOrderPriceRealtime(ctx context.Context, req *model.OrderValidationRequest) (*model.OrderValidationResponse, error) {
	s.logger.Info("执行实时订单价格验证",
		zap.String("order_id", req.OrderID),
		zap.String("express_code", req.ExpressCode))

	// 构造价格查询请求
	priceReq := &model.PriceRequest{
		ExpressType:  req.ExpressCode,
		Provider:     req.Provider,
		DisableCache: true, // 强制不使用缓存
		Sender: model.SenderInfo{
			Province: req.FromProvince,
		},
		Receiver: model.ReceiverInfo{
			Province: req.ToProvince,
		},
		Package: model.PackageInfo{
			Weight: req.Weight,
		},
		CustomerOrderNo: req.OrderID,
	}

	// 查询实时价格
	priceResp, err := s.queryWithRealtimeAPI(ctx, priceReq)
	if err != nil {
		s.logger.Error("实时价格查询失败",
			zap.String("order_id", req.OrderID),
			zap.Error(err))
		return &model.OrderValidationResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "实时价格查询失败: " + err.Error(),
		}, nil
	}

	if !priceResp.Success || len(priceResp.Data) == 0 {
		return &model.OrderValidationResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "未找到有效的实时价格",
		}, nil
	}

	// 获取当前价格
	currentPrice := priceResp.Data[0].Price
	currentPriceDecimal := decimal.NewFromFloat(currentPrice)

	// 由于OrderValidationRequest没有ExpectedPrice字段，我们只记录实时价格
	s.logger.Info("京东快递实时价格验证完成",
		zap.String("order_id", req.OrderID),
		zap.Float64("current_price", currentPrice),
		zap.String("validation_result", "realtime_price_obtained"))

	return &model.OrderValidationResponse{
		Success:          true,
		Code:             model.StatusSuccess,
		Message:          "京东快递实时价格验证完成",
		IsValid:          true, // 实时价格总是有效的
		RealtimePrice:    currentPriceDecimal,
		PriceDifference:  decimal.Zero,
		ValidationResult: "realtime_api_success",
		ActionTaken:      "bypassed_cache_for_jd_express",
	}, nil
}

// getRealtimeQueryReason 获取使用实时查询的原因
func (s *EnhancedPriceService) getRealtimeQueryReason(req *model.PriceRequest) string {
	if !s.enableWeightCache {
		return "缓存未启用"
	}
	if req.DisableCache {
		return "明确禁用缓存"
	}
	if s.isRealtimePriceNoCacheEnabled(req) {
		return "实时查价禁用缓存"
	}
	if req.Package.Weight > s.maxWeightForCache {
		return fmt.Sprintf("重量超出缓存范围(%.1fKG > %.1fKG)", req.Package.Weight, s.maxWeightForCache)
	}
	if req.Sender.Province == "" || req.Receiver.Province == "" {
		return "缺少地址信息"
	}
	return "其他原因"
}

// queryWithWeightCache 使用重量档位缓存查询
func (s *EnhancedPriceService) queryWithWeightCache(ctx context.Context, req *model.PriceRequest, start time.Time) (*model.PriceResponse, error) {
	// 🚀 调试日志：进入queryWithWeightCache
	s.logger.Debug("[DEBUG] queryWithWeightCache called", zap.Any("req.ExpressCodes", req.ExpressCodes), zap.String("req.Provider", req.Provider))

	// 构造缓存查询请求
	expressCode := req.ExpressType
	if expressCode == "" && len(req.ExpressCodes) == 1 {
		expressCode = req.ExpressCodes[0]
	}

	// 🔥 关键修复：在生成缓存键前先计算体积重量
	// 确保缓存键基于计费重量而不是原始重量
	chargedWeight, err := s.calculateChargedWeightForCache(req.Package, expressCode)
	if err != nil {
		s.logger.Warn("快递公司计费重量计算失败，跳过查询",
			zap.String("express_code", expressCode),
			zap.Error(err))
		return nil, err
	}

	s.logger.Info("体积重量缓存计算",
		zap.Float64("original_weight", req.Package.Weight),
		zap.Float64("charged_weight", chargedWeight),
		zap.String("express_code", expressCode))

	// 🔥 修复：如果没有指定供应商，查询所有供应商的价格
	if req.Provider == "" {
		s.logger.Info("单快递公司查询未指定供应商，查询所有供应商价格",
			zap.String("express_code", expressCode))
		return s.querySingleExpressAllProviders(ctx, req, expressCode, chargedWeight, start)
	}

	cacheReq := &model.WeightTierCacheRequest{
		FromProvince: req.Sender.Province,
		ToProvince:   req.Receiver.Province,
		Provider:     req.Provider,
		ExpressCode:  expressCode,
		Weight:       chargedWeight, // 🔥 使用计费重量而不是原始重量
	}

	// 调用重量档位缓存服务
	cacheResp, err := s.weightCacheService.QueryPriceWithCache(ctx, cacheReq)
	if err != nil {
		s.logger.Error("重量档位缓存查询失败", zap.Error(err))

		// 🚀 修复：检查是否因为供应商映射被禁用导致的失败
		if strings.Contains(err.Error(), "不支持快递公司") && strings.Contains(err.Error(), "数据库配置 is_supported=false") {
			s.logger.Info("供应商映射已被禁用，完全跳过该查询",
				zap.String("provider", req.Provider),
				zap.String("express_type", req.ExpressType),
				zap.Error(err))
			return nil, err // 直接返回错误，不进行降级查询
		}

		// 其他错误才进行降级查询
		s.logger.Info("降级到实时查询", zap.Error(err))
		return s.queryWithRealtimeAPI(ctx, req)
	}

	if !cacheResp.Success {
		s.logger.Warn("重量档位缓存查询未成功",
			zap.String("message", cacheResp.Message),
			zap.Int("code", cacheResp.Code))
		// 缓存查询未成功时，降级到实时查询
		s.logger.Info("降级到实时查询")
		return s.queryWithRealtimeAPI(ctx, req)
	}

	// 转换缓存响应为标准价格响应 (优化版 - 包含续重价格和产品信息)
	priceFloat, _ := cacheResp.Data.Price.Float64()
	continuedWeightFloat, _ := cacheResp.Data.ContinuedWeightPerKg.Float64()

	standardizedPrice := model.StandardizedPrice{
		ExpressCode:          cacheResp.Data.ExpressCode,
		ExpressName:          cacheResp.Data.ExpressName,
		ProductCode:          cacheResp.Data.ProductCode,
		ProductName:          cacheResp.Data.ProductName,
		Price:                priceFloat,
		ContinuedWeightPerKg: continuedWeightFloat,
		CalcWeight:           float64(cacheResp.Data.WeightKg),
		Provider:             cacheResp.Data.Provider,
		ChannelID:            cacheResp.Data.ChannelID,
		OrderCode:            generateOrderCode(req.CustomerOrderNo, cacheResp.Data.Provider, cacheResp.Data.ExpressCode),
	}

	responseTime := time.Since(start)
	s.logger.Info("重量档位缓存查询完成",
		zap.String("source", cacheResp.Source),
		zap.String("response_time", cacheResp.ResponseTime),
		zap.Duration("total_time", responseTime),
		zap.Float64("price", standardizedPrice.Price))

	return &model.PriceResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    []model.StandardizedPrice{standardizedPrice},
	}, nil
}

// 🔥 删除缓存：查询所有快递公司价格（无缓存版本）
func (s *EnhancedPriceService) queryAllCompaniesWithCache(ctx context.Context, req *model.PriceRequest, start time.Time) (*model.PriceResponse, error) {
	s.logger.Info("无缓存模式：直接查询所有快递公司价格")
	// 直接调用实时查询
	return s.queryAllCompaniesPrice(ctx, req)
}

// 🔥 删除缓存：比较所有供应商价格（无缓存版本）
func (s *EnhancedPriceService) compareAllProvidersWithCache(ctx context.Context, req *model.PriceRequest, start time.Time) (*model.PriceResponse, error) {
	s.logger.Info("无缓存模式：直接比较所有供应商价格")
	// 直接调用实时查询
	return s.compareAllProvidersPrice(ctx, req)
}

// 🚀 新增：查询多个快递公司价格（带缓存）- 返回所有供应商的价格
func (s *EnhancedPriceService) queryMultipleExpressWithCache(ctx context.Context, req *model.PriceRequest, start time.Time) (*model.PriceResponse, error) {
	// 🚀 调试日志：进入queryMultipleExpressWithCache
	s.logger.Debug("[DEBUG] queryMultipleExpressWithCache called", zap.Any("req.ExpressCodes", req.ExpressCodes), zap.Any("req.Provider", req.Provider))

	// 🚀 性能监控：开始监控
	s.performanceMonitor.IncrementConcurrentQueries()
	defer s.performanceMonitor.DecrementConcurrentQueries()

	s.logger.Info("使用缓存查询多个快递公司价格（所有供应商）",
		zap.Int("express_count", len(req.ExpressCodes)))

	// 🚀 关键修复：动态获取启用的供应商，避免调用已关闭的供应商
	// 🔥 修复：排除菜鸟和快递鸟供应商，它们只在实时查价接口中使用
	allProviders := s.providerCoordinator.GetAllProviders()
	var enabledProviders []adapter.ProviderAdapter
	for _, provider := range allProviders {
		if provider.Name() != "cainiao" && provider.Name() != "kuaidiniao" {
			enabledProviders = append(enabledProviders, provider)
		}
	}

	if len(enabledProviders) == 0 {
		return &model.PriceResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "没有可用的供应商",
		}, nil
	}

	// 提取供应商代码
	var providers []string
	for _, provider := range enabledProviders {
		providers = append(providers, provider.Name())
	}

	s.logger.Info("获取启用的供应商列表",
		zap.Int("enabled_count", len(providers)),
		zap.Strings("enabled_providers", providers))

	var wg sync.WaitGroup
	var mu sync.Mutex
	var allPrices []model.StandardizedPrice
	var cacheHits, cacheMisses int

	// 🚀 超高性能优化：使用批量映射获取，避免重复数据库查询
	mappingStart := time.Now()

	// 使用新的批量映射方法，一次性获取所有供应商映射
	providerMappings, err := s.getBatchProviderMappings(ctx, providers)
	if err != nil {
		s.logger.Error("批量获取供应商映射失败，降级到逐个查询",
			zap.Error(err))
		// 降级到原有逻辑
		providerMappings = s.fallbackGetProviderMappings(ctx, providers)
	}

	mappingLoadTime := time.Since(mappingStart)
	s.performanceMonitor.RecordMappingLoadTime(mappingLoadTime) // 🚀 记录映射加载时间
	s.logger.Info("批量加载供应商映射完成",
		zap.Duration("load_time", mappingLoadTime),
		zap.Int("provider_count", len(providerMappings)))

	// 🚀 高性能预过滤：使用内存映射快速过滤
	var validCombinations []struct {
		ExpressCode string
		Provider    string
	}

	for _, expressCode := range req.ExpressCodes {
		for _, provider := range providers {
			// 快速检查供应商是否支持该快递公司
			if expressMapping, exists := providerMappings[provider]; exists {
				if isSupported, found := expressMapping[expressCode]; found && isSupported {
					validCombinations = append(validCombinations, struct {
						ExpressCode string
						Provider    string
					}{
						ExpressCode: expressCode,
						Provider:    provider,
					})
				} else {
					s.logger.Debug("预过滤：供应商不支持该快递公司，跳过查询",
						zap.String("express_code", expressCode),
						zap.String("provider", provider))
				}
			}
		}
	}

	preFilterTime := time.Since(mappingStart)
	s.performanceMonitor.RecordPreFilterTime(preFilterTime) // 🚀 记录预过滤时间

	s.logger.Info("智能预过滤完成",
		zap.Duration("pre_filter_time", preFilterTime),
		zap.Int("total_combinations", len(req.ExpressCodes)*len(providers)),
		zap.Int("valid_combinations", len(validCombinations)),
		zap.Int("filtered_out", len(req.ExpressCodes)*len(providers)-len(validCombinations)))

	// 为每个有效的快递公司×供应商组合创建查询任务
	for _, combination := range validCombinations {
		wg.Add(1)
		go func(code, prov string) {
			defer wg.Done()

			// 构造单个快递×供应商查询请求
			singleReq := *req
			singleReq.ExpressType = code
			singleReq.ExpressCodes = nil
			singleReq.QueryAllCompanies = false
			singleReq.IsCompare = false
			singleReq.Provider = prov // 🔥 关键修复：指定供应商进行缓存查询

			// 尝试缓存查询
			resp, err := s.queryWithWeightCache(ctx, &singleReq, start)

			mu.Lock()
			if err == nil && resp.Success && len(resp.Data) > 0 {
				// 缓存命中
				cacheHits++
				allPrices = append(allPrices, resp.Data...)
				s.logger.Debug("快递×供应商缓存命中",
					zap.String("express_code", code),
					zap.String("provider", prov),
					zap.String("source", "cache"))
			} else {
				// 🚀 修复：检查是否因为供应商映射被禁用导致的失败
				if err != nil && strings.Contains(err.Error(), "不支持快递公司") && strings.Contains(err.Error(), "数据库配置 is_supported=false") {
					// 供应商映射被禁用，这是正常情况，不计入缓存未命中
					s.logger.Debug("供应商映射已被禁用，跳过该组合",
						zap.String("express_code", code),
						zap.String("provider", prov))
				} else {
					// 真正的缓存未命中或其他错误
					cacheMisses++
					s.logger.Error("快递×供应商缓存查询失败",
						zap.String("express_code", code),
						zap.String("provider", prov),
						zap.Error(err))
				}
			}
			mu.Unlock()
		}(combination.ExpressCode, combination.Provider)
	}

	wg.Wait()

	// 统计缓存命中率
	totalQueries := cacheHits + cacheMisses
	cacheHitRate := 0.0
	if totalQueries > 0 {
		cacheHitRate = float64(cacheHits) / float64(totalQueries) * 100
	}

	s.logger.Info("多快递×供应商查询缓存统计",
		zap.Int("total_queries", totalQueries),
		zap.Int("cache_hits", cacheHits),
		zap.Int("cache_misses", cacheMisses),
		zap.Float64("cache_hit_rate", cacheHitRate))

	// 🚀 修复：只有在有真正的缓存未命中时才报错
	// 供应商不支持某些快递公司是正常情况，不应该导致失败
	if cacheMisses > 0 {
		s.logger.Warn("存在缓存查询失败的情况",
			zap.Int("cache_misses", cacheMisses),
			zap.Float64("cache_hit_rate", cacheHitRate))
		// 注意：这里不再强制要求100%缓存命中率，因为供应商不支持某些快递公司是正常的
	}

	// 🚀 修复：过滤掉实时查价快递价格（统一网关接口不应包含实时查价快递）
	filteredPrices := s.filterOutRealtimePriceExpress(allPrices)

	s.logger.Info("过滤实时查价快递公司完成",
		zap.Int("原始价格数量", len(allPrices)),
		zap.Int("过滤后价格数量", len(filteredPrices)),
		zap.Int("过滤掉数量", len(allPrices)-len(filteredPrices)))

	// 按价格排序
	sort.Slice(filteredPrices, func(i, j int) bool {
		return filteredPrices[i].Price < filteredPrices[j].Price
	})

	// 🚀 性能监控：记录查询性能数据
	totalTime := time.Since(start)
	success := len(filteredPrices) > 0
	cacheHit := cacheHits > 0
	dbQueries := cacheMisses // 缓存未命中次数等于数据库查询次数

	s.performanceMonitor.RecordQuery(ctx, totalTime, success, cacheHit, dbQueries)

	s.logger.Info("查价完成，性能统计",
		zap.Duration("total_time", totalTime),
		zap.Bool("success", success),
		zap.Int("cache_hits", cacheHits),
		zap.Int("cache_misses", cacheMisses),
		zap.Int("result_count", len(filteredPrices)))

	// 🚀 构建基础响应
	return &model.PriceResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    filteredPrices,
	}, nil
}

// getActiveExpressCodes 获取活跃的快递公司代码
// 🚀 完全基于数据库配置，移除硬编码默认列表
func (s *EnhancedPriceService) getActiveExpressCodes(ctx context.Context) ([]string, error) {
	// 从重量档位缓存服务获取快递代码
	allExpressCodes, err := s.weightCacheService.GetActiveExpressCodes(ctx)
	if err != nil {
		s.logger.Error("从缓存服务获取快递代码失败", zap.Error(err))
		return nil, fmt.Errorf("获取活跃快递代码失败: %w。请确保数据库连接正常且express_companies表中存在活跃的快递公司记录", err)
	}

	if len(allExpressCodes) == 0 {
		return nil, fmt.Errorf("数据库中没有找到活跃的快递公司。请在express_companies表中添加快递公司记录并设置is_active=true")
	}

	// 🚀 修复：统一网关接口过滤掉JD和DBL快递公司
	var filteredCodes []string
	for _, code := range allExpressCodes {
		if code != "JD" && code != "DBL" {
			filteredCodes = append(filteredCodes, code)
		} else {
			s.logger.Debug("统一网关接口过滤掉专用快递公司",
				zap.String("express_code", code),
				zap.String("reason", "JD和DBL有专用接口"))
		}
	}

	s.logger.Info("获取活跃快递代码完成（已过滤JD和DBL）",
		zap.Int("原始数量", len(allExpressCodes)),
		zap.Int("过滤后数量", len(filteredCodes)),
		zap.Strings("filtered_codes", filteredCodes))

	return filteredCodes, nil
}

// calculateChargedWeightForCache 缓存版本的重量计算
// 根据包裹尺寸和重量计算计费重量，支持快递公司特定的抛比配置
func (s *EnhancedPriceService) calculateChargedWeightForCache(pkg model.PackageInfo, expressCode string) (float64, error) {
	// 如果没有提供尺寸信息，直接返回实际重量
	if pkg.Length <= 0 || pkg.Width <= 0 || pkg.Height <= 0 {
		return pkg.Weight, nil
	}

	// 计算体积（cm³）
	volumeCm3 := pkg.Length * pkg.Width * pkg.Height

	// 🔥 关键修复：从数据库动态获取快递公司的抛比配置
	volumeRatio, err := s.getVolumeWeightRatio(expressCode)
	if err != nil {
		s.logger.Warn("快递公司抛比配置获取失败，跳过该快递公司",
			zap.String("express_code", expressCode),
			zap.Error(err))
		return 0, err
	}

	// 使用快递公司特定的抛比计算体积重量
	volumeWeight := volumeCm3 / float64(volumeRatio)

	// 返回实际重量和体积重量的较大值
	chargedWeight := pkg.Weight
	if volumeWeight > pkg.Weight {
		chargedWeight = volumeWeight
	}

	s.logger.Debug("缓存体积重量计算",
		zap.String("express_code", expressCode),
		zap.Float64("length", pkg.Length),
		zap.Float64("width", pkg.Width),
		zap.Float64("height", pkg.Height),
		zap.Float64("volume_cm3", volumeCm3),
		zap.Int("volume_ratio", volumeRatio),
		zap.Float64("volume_weight", volumeWeight),
		zap.Float64("actual_weight", pkg.Weight),
		zap.Float64("charged_weight", chargedWeight))

	return chargedWeight, nil
}

// getVolumeWeightRatio 获取快递公司的抛比配置
// 严格从数据库获取，如果快递公司被禁用则返回错误
func (s *EnhancedPriceService) getVolumeWeightRatio(expressCode string) (int, error) {
	// 🚀 调试日志：进入抛比配置获取
	s.logger.Debug("[DEBUG] getVolumeWeightRatio called", zap.String("express_code", expressCode))
	if s.volumeCalc != nil {
		ratio, err := s.volumeCalc.GetVolumeWeightRatio(expressCode)
		if err != nil {
			s.logger.Warn("获取快递公司抛比配置失败，跳过该快递公司",
				zap.String("express_code", expressCode),
				zap.Error(err))
			return 0, fmt.Errorf("快递公司 %s 抛比配置获取失败: %w", expressCode, err)
		}
		s.logger.Debug("[DEBUG] getVolumeWeightRatio success", zap.String("express_code", expressCode), zap.Int("volume_ratio", ratio))
		return ratio, nil
	}

	// 如果体积重量计算器未初始化，返回错误
	s.logger.Error("体积重量计算器未初始化", zap.String("express_code", expressCode))
	return 0, fmt.Errorf("体积重量计算器未初始化，无法获取快递公司 %s 的抛比配置", expressCode)
}

// 🚀 新增：获取所有供应商支持的快递公司代码
func (s *EnhancedPriceService) getAllSupportedExpressCodes(ctx context.Context) ([]string, error) {
	// 🔍 详细调试：记录开始获取所有支持的快递代码
	s.logger.Info("🚀 [DEBUG] 开始获取所有供应商支持的快递公司代码")

	// 获取所有供应商
	// 🔥 修复：排除菜鸟和快递鸟供应商，它们只在实时查价接口中使用
	allProviders := s.providerCoordinator.GetAllProviders()
	var providers []adapter.ProviderAdapter
	for _, provider := range allProviders {
		if provider.Name() != "cainiao" && provider.Name() != "kuaidiniao" {
			providers = append(providers, provider)
		}
	}

	if len(providers) == 0 {
		s.logger.Error("❌ [ERROR] 没有可用的供应商")
		return nil, fmt.Errorf("没有可用的供应商")
	}

	// 🔍 详细调试：显示所有获取到的供应商
	s.logger.Info("📋 [DEBUG] 获取到的供应商列表", zap.Int("count", len(providers)))
	for i, provider := range providers {
		providerName := provider.Name()
		s.logger.Info(fmt.Sprintf("   %d. %s", i+1, providerName))
	}

	// 使用 map 去重
	expressCodesSet := make(map[string]bool)

	// 遍历所有供应商，获取每个供应商支持的快递公司
	for _, provider := range providers {
		providerCode := provider.Name()
		s.logger.Info("🔍 [DEBUG] 获取供应商支持的快递公司", zap.String("provider", providerCode))

		// 获取该供应商支持的快递公司列表
		supportedCompanies, err := s.providerCoordinator.GetSupportedCompanies(ctx, providerCode)
		if err != nil {
			s.logger.Warn("❌ [ERROR] 获取供应商支持的快递公司失败",
				zap.String("provider", providerCode),
				zap.Error(err))
			// 🔍 特别关注快递鸟的错误
			if providerCode == "kuaidiniao" {
				s.logger.Error("🚨 [CRITICAL] 快递鸟映射获取失败！", zap.Error(err))
			}
			continue
		}

		// 🔍 详细调试：记录每个供应商支持的快递公司详情
		s.logger.Info("📊 [DEBUG] 供应商支持的快递公司详情",
			zap.String("provider", providerCode),
			zap.Int("count", len(supportedCompanies)))

		for i, mapping := range supportedCompanies {
			s.logger.Info(fmt.Sprintf("   %d. %s -> %s", i+1, mapping.CompanyCode, mapping.ProviderCompanyCode))
		}

		// 添加到集合中
		addedCodes := 0
		for _, mapping := range supportedCompanies {
			// 🔥 关键修复：只添加启用的映射
			if mapping.CompanyCode != "" && mapping.IsSupported {
				expressCodesSet[mapping.CompanyCode] = true
				addedCodes++
			}
		}

		s.logger.Info("✅ [DEBUG] 供应商快递代码添加完成",
			zap.String("provider", providerCode),
			zap.Int("added_codes", addedCodes),
			zap.Int("total_unique_codes", len(expressCodesSet)))
	}

	// 转换为切片，并过滤掉JD和DBL快递公司
	var expressCodes []string
	for code := range expressCodesSet {
		// 🚀 修复：统一网关接口排除JD和DBL快递公司
		if code != "JD" && code != "DBL" {
			expressCodes = append(expressCodes, code)
		} else {
			s.logger.Debug("统一网关接口过滤掉专用快递公司",
				zap.String("express_code", code),
				zap.String("reason", "JD和DBL有专用接口"))
		}
	}

	// 🔍 详细调试：显示最终的快递代码列表
	s.logger.Info("📈 [DEBUG] 最终的快递公司代码列表（已过滤JD和DBL）",
		zap.Int("total_providers", len(providers)),
		zap.Int("total_express_codes", len(expressCodes)),
		zap.Strings("express_codes", expressCodes))

	return expressCodes, nil
}

// queryWithRealtimeAPI 使用实时API查询 (复用原有逻辑)
func (s *EnhancedPriceService) queryWithRealtimeAPI(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	// 根据查询模式选择不同的处理方式
	if req.QueryAllCompanies {
		return s.queryAllCompaniesPrice(ctx, req)
	} else if req.IsCompare {
		return s.compareAllProvidersPrice(ctx, req)
	} else if len(req.ExpressCodes) > 0 {
		return s.queryMultipleExpressPrice(ctx, req)
	} else {
		return s.queryBestProviderPrice(ctx, req)
	}
}

// ValidateOrderPriceWithCache 带缓存的订单价格验证
// 新增功能：在下单时验证缓存价格的准确性
func (s *EnhancedPriceService) ValidateOrderPriceWithCache(ctx context.Context, req *model.OrderValidationRequest) (*model.OrderValidationResponse, error) {
	s.logger.Info("开始订单价格验证",
		zap.String("order_id", req.OrderID),
		zap.String("price_source", req.PriceSource))

	// 检查是否是实时查价订单，如果是则跳过缓存验证，直接进行实时验证
	if s.isRealtimePriceOrder(req) {
		s.logger.Info("实时查价订单，跳过缓存验证，使用实时价格验证",
			zap.String("express_code", req.ExpressCode),
			zap.String("order_id", req.OrderID))
		return s.validateOrderPriceRealtime(ctx, req)
	}

	return s.weightCacheService.ValidateOrderPrice(ctx, req)
}

// GetCacheStatistics 获取缓存统计
func (s *EnhancedPriceService) GetCacheStatistics(ctx context.Context, req *model.CacheStatisticsRequest) (*model.CacheStatisticsResponse, error) {
	return s.weightCacheService.GetCacheStatistics(ctx, req)
}

// GetCacheOverview 获取缓存概览
func (s *EnhancedPriceService) GetCacheOverview(ctx context.Context) ([]*model.WeightCacheOverview, error) {
	return s.weightCacheService.GetCacheOverview(ctx)
}

// InvalidatePriceCache 使价格缓存失效
func (s *EnhancedPriceService) InvalidatePriceCache(ctx context.Context, fromProvince, toProvince, provider, expressCode string, weight float64) error {
	return s.weightCacheService.InvalidateCacheEntry(ctx, fromProvince, toProvince, provider, expressCode, weight)
}

// WarmupPriceCache 预热价格缓存
func (s *EnhancedPriceService) WarmupPriceCache(ctx context.Context, routes []*model.RouteDefinition, providers []string, weights []int) error {
	return s.weightCacheService.WarmupCache(ctx, routes, providers, weights, nil)
}

// SetCacheEnabled 设置缓存启用状态
func (s *EnhancedPriceService) SetCacheEnabled(enabled bool) {
	s.enableWeightCache = enabled
	s.logger.Info("缓存状态已更新", zap.Bool("enabled", enabled))
}

// SetMaxWeightForCache 设置缓存支持的最大重量
func (s *EnhancedPriceService) SetMaxWeightForCache(maxWeight float64) {
	s.maxWeightForCache = maxWeight
	s.logger.Info("缓存最大重量已更新", zap.Float64("max_weight", maxWeight))
}

// 以下方法复用原有price_service.go的实现

// queryAllCompaniesPrice 查询所有快递公司价格
func (s *EnhancedPriceService) queryAllCompaniesPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	allPrices, err := s.providerCoordinator.QueryAllCompanies(ctx, req)
	if err != nil {
		return &model.PriceResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: err.Error(),
		}, nil
	}

	return &model.PriceResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    allPrices,
	}, nil
}

// compareAllProvidersPrice 比较所有供应商价格
func (s *EnhancedPriceService) compareAllProvidersPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	allPrices, err := s.providerCoordinator.QueryAllProviders(ctx, req)
	if err != nil {
		return &model.PriceResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: err.Error(),
		}, nil
	}

	return &model.PriceResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    allPrices,
	}, nil
}

// queryBestProviderPrice 查询最优供应商价格
func (s *EnhancedPriceService) queryBestProviderPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	var allPrices []model.StandardizedPrice
	var err error

	if req.Provider != "" {
		allPrices, err = s.providerCoordinator.QuerySingleProvider(ctx, req, req.Provider)
		if err != nil {
			return &model.PriceResponse{
				Success: false,
				Code:    model.StatusInternalServerError,
				Message: err.Error(),
			}, nil
		}
	} else {
		allPrices, err = s.providerCoordinator.QueryAllProviders(ctx, req)
		if err != nil {
			return &model.PriceResponse{
				Success: false,
				Code:    model.StatusInternalServerError,
				Message: err.Error(),
			}, nil
		}

		if len(allPrices) > 0 {
			allPrices = []model.StandardizedPrice{allPrices[0]}
		}
	}

	return &model.PriceResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    allPrices,
	}, nil
}

// queryMultipleExpressPrice 查询多个快递公司价格
func (s *EnhancedPriceService) queryMultipleExpressPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	var wg sync.WaitGroup
	var mu sync.Mutex
	var allPrices []model.StandardizedPrice
	var errs []error

	for _, expressCode := range req.ExpressCodes {
		wg.Add(1)
		go func(code string) {
			defer wg.Done()

			singleReq := *req
			singleReq.ExpressType = code
			singleReq.ExpressCodes = nil

			resp, err := s.queryBestProviderPrice(ctx, &singleReq)
			if err != nil {
				mu.Lock()
				errs = append(errs, fmt.Errorf("查询快递公司 %s 价格失败: %w", code, err))
				mu.Unlock()
				return
			}

			if resp.Success && len(resp.Data) > 0 {
				mu.Lock()
				allPrices = append(allPrices, resp.Data...)
				mu.Unlock()
			}
		}(expressCode)
	}

	wg.Wait()

	if len(allPrices) == 0 && len(errs) > 0 {
		return &model.PriceResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: fmt.Sprintf("所有查询都失败: %v", errs[0]),
		}, nil
	}

	// 🚀 新增：过滤掉实时查价快递价格
	filteredPrices := s.filterOutRealtimePriceExpress(allPrices)

	sort.Slice(filteredPrices, func(i, j int) bool {
		return filteredPrices[i].Price < filteredPrices[j].Price
	})

	return &model.PriceResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    filteredPrices,
	}, nil
}

// generateOrderCode 生成订单代码
func generateOrderCode(customerOrderNo, provider, expressCode string) string {
	if customerOrderNo != "" {
		return fmt.Sprintf("%s_%s_%s", customerOrderNo, provider, expressCode)
	}
	return fmt.Sprintf("%s_%s_%d", provider, expressCode, util.NowBeijing().Unix())
}

// GetProviderManager 获取供应商管理器
func (s *EnhancedPriceService) GetProviderManager() *adapter.ProviderManager {
	return s.providerCoordinator.GetProviderManager()
}

// ComparePrice 价格比较
func (s *EnhancedPriceService) ComparePrice(ctx context.Context, req *model.PriceRequest) (*model.PriceComparisonResponse, error) {
	compareReq := *req
	compareReq.IsCompare = true

	resp, err := s.QueryPrice(ctx, &compareReq)
	if err != nil {
		return &model.PriceComparisonResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: err.Error(),
		}, nil
	}

	if !resp.Success {
		return &model.PriceComparisonResponse{
			Success: resp.Success,
			Code:    resp.Code,
			Message: resp.Message,
		}, nil
	}

	var comparisonData []*model.PriceComparisonData
	for i, price := range resp.Data {
		savings := float64(0)
		if i > 0 {
			savings = price.Price - resp.Data[0].Price
		}

		comparisonData = append(comparisonData, &model.PriceComparisonData{
			Provider:    price.Provider,
			ExpressType: price.ExpressCode,
			Price:       price.Price,
			TimeLimit:   "",
			Ranking:     i + 1,
			Savings:     savings,
		})
	}

	return &model.PriceComparisonResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    comparisonData,
	}, nil
}

// GetBestPrice 获取最优价格
func (s *EnhancedPriceService) GetBestPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceInfo, error) {
	resp, err := s.QueryPrice(ctx, req)
	if err != nil {
		return nil, err
	}

	if !resp.Success || len(resp.Data) == 0 {
		return nil, fmt.Errorf("没有找到可用的价格")
	}

	bestPrice := resp.Data[0]
	return &model.PriceInfo{
		Provider:    bestPrice.Provider,
		ExpressType: bestPrice.ExpressCode,
		Price:       bestPrice.Price,
		TimeLimit:   "",
		ChannelID:   bestPrice.ChannelID,
		Available:   true,
	}, nil
}

// QueryPriceWithCache 带缓存的价格查询（接口兼容性方法）
func (s *EnhancedPriceService) QueryPriceWithCache(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	// 直接调用 QueryPrice，因为它已经集成了缓存逻辑
	return s.QueryPrice(ctx, req)
}

// ClearCache 清除缓存（接口兼容性方法）
func (s *EnhancedPriceService) ClearCache(ctx context.Context, cacheKey string) error {
	s.logger.Info("清除缓存", zap.String("cache_key", cacheKey))
	// 这里可以根据需要实现具体的缓存清除逻辑
	// 目前返回成功，保持接口兼容性
	return nil
}

// WarmupCache 预热缓存（接口兼容性方法）
func (s *EnhancedPriceService) WarmupCache(ctx context.Context, routes []model.RouteInfo) error {
	s.logger.Info("预热缓存", zap.Int("route_count", len(routes)))

	// 转换路由格式并调用重量档位缓存服务
	var routeDefs []*model.RouteDefinition
	for _, route := range routes {
		// RouteInfo 结构体包含 From/To 字段，需要提取省份信息
		var fromProvince, toProvince string
		if route.From != nil {
			fromProvince = route.From.Name
		}
		if route.To != nil {
			toProvince = route.To.Name
		}

		if fromProvince != "" && toProvince != "" {
			routeDefs = append(routeDefs, &model.RouteDefinition{
				FromProvince: fromProvince,
				ToProvince:   toProvince,
			})
		}
	}

	// 从数据库获取供应商和重量配置
	providers, err := s.getActiveProviders(ctx)
	if err != nil {
		s.logger.Error("获取活跃供应商失败", zap.Error(err))
		return err
	}

	weights, err := s.getActiveWeights(ctx)
	if err != nil {
		s.logger.Error("获取活跃重量档位失败", zap.Error(err))
		return err
	}

	return s.weightCacheService.WarmupCache(ctx, routeDefs, providers, weights, nil)
}

// GetPriceStatistics 获取价格统计（接口兼容性方法）
func (s *EnhancedPriceService) GetPriceStatistics(ctx context.Context, req *model.PriceStatisticsRequest) (*model.PriceStatisticsResponse, error) {
	s.logger.Info("获取价格统计")

	// 从重量档位缓存服务获取统计信息
	cacheStats, err := s.weightCacheService.GetCacheStatistics(ctx, &model.CacheStatisticsRequest{
		Provider:  req.Provider,
		StartDate: req.StartTime,
		EndDate:   req.EndTime,
	})
	if err != nil {
		s.logger.Error("获取缓存统计失败", zap.Error(err))
		return &model.PriceStatisticsResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "获取价格统计失败: " + err.Error(),
		}, err
	}

	// 转换统计数据格式
	stats := &model.PriceStatisticsData{
		TotalQueries:   cacheStats.Data.TotalQueries,
		AveragePrice:   0.0, // 缓存统计中没有平均价格，设为0
		MinPrice:       0.0, // 缓存统计中没有最小价格，设为0
		MaxPrice:       0.0, // 缓存统计中没有最大价格，设为0
		ProviderStats:  make(map[string]*model.ProviderStat),
		TimeSeriesData: make([]*model.TimeSeriesPoint, 0),
	}

	// 转换供应商统计
	for provider, stat := range cacheStats.Data.ProviderStats {
		stats.ProviderStats[provider] = &model.ProviderStat{
			QueryCount:   stat.TotalQueries,
			AveragePrice: 0.0, // 缓存统计中没有平均价格，设为0
			SuccessRate:  float64(stat.CacheHitRate.InexactFloat64()),
		}
	}

	return &model.PriceStatisticsResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "价格统计查询成功",
		Data:    stats,
	}, nil
}

// getActiveProviders 从数据库获取活跃的供应商列表
func (s *EnhancedPriceService) getActiveProviders(ctx context.Context) ([]string, error) {
	// 从系统配置获取活跃供应商
	configValue, err := s.configService.GetConfig("price_query.active_providers")
	if err != nil {
		s.logger.Error("获取活跃供应商配置失败", zap.Error(err))
		return nil, fmt.Errorf("获取活跃供应商配置失败: %w", err)
	}

	if configValue == "" {
		return nil, fmt.Errorf("活跃供应商配置不存在，请在数据库中配置 price_query.active_providers")
	}

	// 解析供应商列表（假设是逗号分隔的字符串）
	providers := strings.Split(configValue, ",")
	for i := range providers {
		providers[i] = strings.TrimSpace(providers[i])
	}

	s.logger.Info("获取活跃供应商成功", zap.Strings("providers", providers))
	return providers, nil
}

// getActiveWeights 从数据库获取活跃的重量档位列表
func (s *EnhancedPriceService) getActiveWeights(ctx context.Context) ([]int, error) {
	// 从系统配置获取活跃重量档位
	configValue, err := s.configService.GetConfig("price_query.active_weights")
	if err != nil {
		s.logger.Error("获取活跃重量档位配置失败", zap.Error(err))
		return nil, fmt.Errorf("获取活跃重量档位配置失败: %w", err)
	}

	if configValue == "" {
		return nil, fmt.Errorf("活跃重量档位配置不存在，请在数据库中配置 price_query.active_weights")
	}

	// 解析重量档位列表（假设是逗号分隔的字符串）
	weightStrs := strings.Split(configValue, ",")
	weights := make([]int, 0, len(weightStrs))

	for _, weightStr := range weightStrs {
		weightStr = strings.TrimSpace(weightStr)
		if weight, err := strconv.Atoi(weightStr); err == nil {
			weights = append(weights, weight)
		} else {
			s.logger.Warn("无效的重量档位配置", zap.String("weight", weightStr), zap.Error(err))
		}
	}

	if len(weights) == 0 {
		return nil, fmt.Errorf("没有有效的重量档位配置，请检查数据库中的 price_query.active_weights 配置")
	}

	s.logger.Info("获取活跃重量档位成功", zap.Ints("weights", weights))
	return weights, nil
}

// querySingleExpressAllProviders 查询单个快递公司在所有供应商的价格
func (s *EnhancedPriceService) querySingleExpressAllProviders(ctx context.Context, req *model.PriceRequest, expressCode string, chargedWeight float64, start time.Time) (*model.PriceResponse, error) {
	// 获取启用的供应商列表
	// 🔥 修复：排除菜鸟和快递鸟供应商，它们只在实时查价接口中使用
	allProviders := s.providerCoordinator.GetAllProviders()
	var enabledProviders []adapter.ProviderAdapter
	for _, provider := range allProviders {
		if provider.Name() != "cainiao" && provider.Name() != "kuaidiniao" {
			enabledProviders = append(enabledProviders, provider)
		}
	}

	if len(enabledProviders) == 0 {
		s.logger.Error("没有启用的供应商")
		return s.queryWithRealtimeAPI(ctx, req)
	}

	var providers []string
	for _, provider := range enabledProviders {
		providers = append(providers, provider.Name())
	}

	s.logger.Info("单快递公司查询所有供应商",
		zap.String("express_code", expressCode),
		zap.Strings("providers", providers),
		zap.Float64("charged_weight", chargedWeight))

	var allPrices []model.StandardizedPrice
	var cacheHits, cacheMisses int

	// 并发查询所有供应商
	var wg sync.WaitGroup
	var mu sync.Mutex

	for _, provider := range enabledProviders {
		wg.Add(1)
		go func(prov string) {
			defer wg.Done()

			cacheReq := &model.WeightTierCacheRequest{
				FromProvince: req.Sender.Province,
				ToProvince:   req.Receiver.Province,
				Provider:     prov,
				ExpressCode:  expressCode,
				Weight:       chargedWeight,
			}

			// 尝试缓存查询
			cacheResp, err := s.weightCacheService.QueryPriceWithCache(ctx, cacheReq)

			mu.Lock()
			defer mu.Unlock()

			if err == nil && cacheResp.Success && cacheResp.Data != nil {
				// 缓存命中
				cacheHits++
				priceFloat, _ := cacheResp.Data.Price.Float64()
				continuedWeightFloat, _ := cacheResp.Data.ContinuedWeightPerKg.Float64()

				// 生成订单代码
				orderCode := generateOrderCode(req.CustomerOrderNo, prov, expressCode)

				standardizedPrice := model.StandardizedPrice{
					ExpressCode:          cacheResp.Data.ExpressCode,
					ExpressName:          cacheResp.Data.ExpressName,
					ProductCode:          cacheResp.Data.ProductCode,
					ProductName:          cacheResp.Data.ProductName,
					Price:                priceFloat,
					ContinuedWeightPerKg: continuedWeightFloat,
					CalcWeight:           chargedWeight,
					OrderCode:            orderCode,
					Provider:             prov,
					ChannelID:            cacheResp.Data.ChannelID,
					EstimatedDays:        cacheResp.Data.EstimatedDays,
				}
				allPrices = append(allPrices, standardizedPrice)

				s.logger.Info("单快递缓存命中",
					zap.String("provider", prov),
					zap.String("express_code", expressCode),
					zap.Float64("price", priceFloat))
			} else {
				// 缓存未命中，记录但不进行实时查询（保持缓存策略的纯净性）
				cacheMisses++
				s.logger.Info("单快递缓存未命中",
					zap.String("provider", prov),
					zap.String("express_code", expressCode),
					zap.String("reason", func() string {
						if err != nil {
							return err.Error()
						}
						if !cacheResp.Success {
							return cacheResp.Message
						}
						return "无数据"
					}()))
			}
		}(provider.Name())
	}

	wg.Wait()

	// 记录缓存统计
	s.logger.Info("单快递多供应商缓存统计",
		zap.String("express_code", expressCode),
		zap.Int("cache_hits", cacheHits),
		zap.Int("cache_misses", cacheMisses),
		zap.Int("total_providers", len(enabledProviders)))

	// 🔥 修复：智能判断是否应该降级到实时查询
	if len(allPrices) == 0 {
		s.logger.Warn("所有供应商缓存都未命中，检查是否有启用的映射",
			zap.String("express_code", expressCode),
			zap.Int("cache_hits", cacheHits),
			zap.Int("cache_misses", cacheMisses))

		// 🔥 关键修复：检查是否有任何供应商支持该快递公司
		hasEnabledMapping := false
		allProviders := s.providerCoordinator.GetAllProviders()

		for _, provider := range allProviders {
			// 通过供应商协调器检查映射状态
			if supportedCompanies, err := s.providerCoordinator.GetSupportedCompanies(ctx, provider.Name()); err == nil {
				for _, mapping := range supportedCompanies {
					if mapping.CompanyCode == expressCode && mapping.IsSupported {
						hasEnabledMapping = true
						s.logger.Info("发现启用的映射，允许降级到实时查询",
							zap.String("provider", provider.Name()),
							zap.String("express_code", expressCode))
						break
					}
				}
				if hasEnabledMapping {
					break
				}
			}
		}

		if !hasEnabledMapping {
			s.logger.Warn("没有找到任何启用的映射，不降级到实时查询",
				zap.String("express_code", expressCode))
			return &model.PriceResponse{
				Success: false,
				Code:    404,
				Message: fmt.Sprintf("快递公司 %s 在所有启用的供应商中都不可用", expressCode),
				Data:    []model.StandardizedPrice{},
			}, nil
		}

		s.logger.Info("发现启用的映射，降级到实时查询",
			zap.String("express_code", expressCode))
		return s.queryWithRealtimeAPI(ctx, req)
	}

	// 构造响应
	response := &model.PriceResponse{
		Success: true,
		Code:    200,
		Message: "成功",
		Data:    allPrices,
	}

	return response, nil
}

// filterOutRealtimePriceExpress 过滤掉实时查价快递的价格
func (s *EnhancedPriceService) filterOutRealtimePriceExpress(prices []model.StandardizedPrice) []model.StandardizedPrice {
	var filteredPrices []model.StandardizedPrice
	for _, price := range prices {
		// 过滤掉京东快递
		if strings.Contains(price.ExpressCode, "JD") || strings.Contains(price.ExpressCode, "jd") ||
			strings.Contains(price.ExpressCode, "jingdong") || strings.Contains(price.ExpressCode, "JINGDONG") ||
			strings.Contains(price.ExpressCode, "京东") || strings.Contains(price.ExpressCode, "京东快递") ||
			strings.Contains(price.ExpressCode, "jd_express") {
			s.logger.Debug("过滤掉实时查价快递价格", zap.String("express_code", price.ExpressCode))
			continue
		}

		// 过滤掉德邦快递
		if strings.Contains(price.ExpressCode, "DBL") || strings.Contains(price.ExpressCode, "dhl") ||
			strings.Contains(price.ExpressCode, "dhl_express") || strings.Contains(price.ExpressCode, "dhl_express_cn") {
			s.logger.Debug("过滤掉德邦快递价格", zap.String("express_code", price.ExpressCode))
			continue
		}

		filteredPrices = append(filteredPrices, price)
	}
	return filteredPrices
}

// 🚀 新增：批量获取供应商映射，使用缓存服务的高性能方法
func (s *EnhancedPriceService) getBatchProviderMappings(ctx context.Context, providers []string) (map[string]map[string]bool, error) {
	// 通过映射服务获取批量映射
	if mappingService, ok := s.providerCoordinator.mappingService.(interface {
		GetAllProviderMappings(ctx context.Context, providers []string) (map[string]map[string]bool, error)
	}); ok {
		return mappingService.GetAllProviderMappings(ctx, providers)
	}

	return nil, fmt.Errorf("映射服务不支持批量获取")
}

// 🚀 新增：降级方法，逐个获取供应商映射
func (s *EnhancedPriceService) fallbackGetProviderMappings(ctx context.Context, providers []string) map[string]map[string]bool {
	providerMappings := make(map[string]map[string]bool)
	var mappingMutex sync.Mutex
	var wg sync.WaitGroup

	// 并发获取所有供应商的映射关系
	for _, provider := range providers {
		wg.Add(1)
		go func(providerName string) {
			defer wg.Done()

			supportedCompanies, err := s.providerCoordinator.GetSupportedCompanies(ctx, providerName)
			if err != nil {
				s.logger.Warn("获取供应商支持的快递公司失败，跳过该供应商",
					zap.String("provider", providerName),
					zap.Error(err))
				return
			}

			// 构建快速查找映射
			expressMapping := make(map[string]bool)
			for _, mapping := range supportedCompanies {
				expressMapping[mapping.CompanyCode] = mapping.IsSupported
			}

			// 线程安全地存储映射
			mappingMutex.Lock()
			providerMappings[providerName] = expressMapping
			mappingMutex.Unlock()
		}(provider)
	}

	wg.Wait()
	return providerMappings
}

// 🚀 新增：性能监控相关方法

// GetPerformanceMetrics 获取性能指标
func (s *EnhancedPriceService) GetPerformanceMetrics() *PriceQueryMetrics {
	if s.performanceMonitor == nil {
		return nil
	}
	return s.performanceMonitor.GetMetrics()
}

// GetPerformanceReport 获取性能报告
func (s *EnhancedPriceService) GetPerformanceReport() map[string]interface{} {
	if s.performanceMonitor == nil {
		return nil
	}
	return s.performanceMonitor.GetPerformanceReport()
}

// ResetPerformanceMetrics 重置性能指标
func (s *EnhancedPriceService) ResetPerformanceMetrics() error {
	if s.performanceMonitor == nil {
		return fmt.Errorf("性能监控器未初始化")
	}
	s.performanceMonitor.Reset()
	return nil
}
